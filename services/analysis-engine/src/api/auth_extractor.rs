//! Production-ready authentication using Tower middleware pattern
//! This avoids the complex FromRequestParts lifetime issues

use crate::api::{
    errors::{ErrorResponse, ErrorType},
    AppState,
    middleware::rbac_queries::get_rbac_query_manager,
};
use crate::audit::{AuditAction, AuditEventBuilder, AuditLogger, AuditOutcome, AuditSeverity};
use crate::errors::AnalysisError;

use axum::{
    extract::Request,
    http::{header, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};

use jsonwebtoken::{decode, decode_header, Algorithm, DecodingKey, Validation};
#[cfg(feature = "security-storage")]
use crate::crypto::rsa_manager::get_rsa_key_manager;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::task::{Context, Poll};
use uuid::Uuid;
use tracing::warn;
use crate::api::middleware::rbac_queries::User;
use crate::storage::gcp_clients::create_spanner_client;
use crate::config::GcpSettings;

use futures::future::BoxFuture;
use tower::{Layer, Service};

/// Authenticated user information extracted from the request
#[derive(Debug, Clone)]
pub struct AuthUser {
    pub user_id: String,
    pub rate_limit: i64,
    pub auth_method: AuthMethod,
    pub roles: Vec<String>,                           // User's assigned roles
    #[cfg(feature = "security-storage")]
    pub permissions: Vec<crate::models::security::Permission>, // Computed permissions
    #[cfg(not(feature = "security-storage"))]
    pub permissions: Vec<String>, // Simplified permissions when security-storage is disabled
    pub scopes: Vec<String>,                         // OAuth2 scopes
}

#[derive(Debug, Clone, PartialEq)]
pub enum AuthMethod {
    ApiKey,
    JwtToken,
    Unknown,
}

/// JWT Claims structure
#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,                // Subject (user ID)
    exp: u64,                   // Expiration time
    iat: u64,                   // Issued at
    aud: String,                // Audience
    iss: String,                // Issuer
    nbf: Option<u64>,           // Not before
    jti: Option<String>,        // JWT ID for revocation tracking
    scope: Option<String>,      // Token scope/permissions
    session_id: Option<String>, // Session identifier for revocation
    device_id: Option<String>,  // Device fingerprint for binding
}

/// Authentication error that can be converted to a response
#[derive(Debug)]
pub struct AuthError {
    pub error_type: ErrorType,
    pub message: String,
    pub status_code: StatusCode,
    pub correlation_id: String,
}

impl IntoResponse for AuthError {
    fn into_response(self) -> Response {
        let error_code = match &self.error_type {
            ErrorType::Authentication => "AUTH_FAILED",
            ErrorType::RateLimit => "RATE_LIMIT_EXCEEDED",
            _ => "AUTH_ERROR",
        }
        .to_string();

        let mut error_response = ErrorResponse::new(self.error_type, self.message);
        error_response.error_code = Some(error_code);
        error_response.user_message =
            Some("Authentication failed. Please check your credentials.".to_string());
        error_response.correlation_id = Some(self.correlation_id);

        (self.status_code, error_response).into_response()
    }
}

/// Production authentication middleware using Tower pattern
pub async fn auth_middleware(mut req: Request, next: Next) -> Response {
    let correlation_id = Uuid::new_v4().to_string();

    // Extract state from request extensions
    let state = match req.extensions().get::<Arc<AppState>>() {
        Some(s) => s.clone(),
        None => {
            let error = ErrorResponse::new(
                ErrorType::Internal,
                "Application state not found".to_string(),
            );
            return (StatusCode::INTERNAL_SERVER_ERROR, Json(error)).into_response();
        }
    };

    // Extract authentication from headers
    let auth_result = authenticate_request(&req, &state, &correlation_id).await;

    match auth_result {
        Ok(user) => {
            // Insert authenticated user into request extensions
            req.extensions_mut().insert(user);
            next.run(req).await
        }
        Err(auth_error) => {
            // Log authentication failure
            audit_auth_failure(&state, None, AuthMethod::Unknown, &auth_error.message).await;
            auth_error.into_response()
        }
    }
}

/// Optional authentication middleware - allows unauthenticated requests
pub async fn optional_auth_middleware(mut req: Request, next: Next) -> Response {
    let correlation_id = Uuid::new_v4().to_string();

    // Extract state from request extensions
    if let Some(state) = req.extensions().get::<Arc<AppState>>() {
        let state = state.clone();

        // Try to authenticate but don't fail if not present
        if let Ok(user) = authenticate_request(&req, &state, &correlation_id).await {
            req.extensions_mut().insert(user);
        }
    }
    // If no state found, just continue without authentication

    next.run(req).await
}

/// Extract and validate authentication from request (owned version for Tower Service)
#[allow(dead_code)]
async fn authenticate_request_owned(
    req: Request,
    state: Arc<AppState>,
    correlation_id: String,
) -> Result<AuthUser, AuthError> {
    // Check for API key first
    if let Some(api_key) = req.headers().get("x-api-key").and_then(|v| v.to_str().ok()) {
        return validate_api_key(api_key, &state, &correlation_id).await;
    }

    // Check for Bearer token
    if let Some(auth_header) = req
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|v| v.to_str().ok())
    {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            return validate_jwt_token(token, &state, &correlation_id, &req).await;
        }
    }

    // No authentication provided
    Err(AuthError {
        error_type: ErrorType::Authentication,
        message: "No authentication provided".to_string(),
        status_code: StatusCode::UNAUTHORIZED,
        correlation_id,
    })
}

/// Extract and validate authentication from request
async fn authenticate_request(
    req: &Request,
    state: &Arc<AppState>,
    correlation_id: &str,
) -> Result<AuthUser, AuthError> {
    // Check for API key first
    if let Some(api_key) = req.headers().get("x-api-key").and_then(|v| v.to_str().ok()) {
        return validate_api_key(api_key, state, correlation_id).await;
    }

    // Check for Bearer token
    if let Some(auth_header) = req
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|v| v.to_str().ok())
    {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            return validate_jwt_token(token, state, correlation_id, req).await;
        }
    }

    // No authentication provided
    Err(AuthError {
        error_type: ErrorType::Authentication,
        message: "No authentication provided".to_string(),
        status_code: StatusCode::UNAUTHORIZED,
        correlation_id: correlation_id.to_string(),
    })
}

/// Extension trait to extract authenticated user from request
pub trait AuthRequestExt {
    fn auth_user(&self) -> Result<&AuthUser, AnalysisError>;
    fn optional_auth_user(&self) -> Option<&AuthUser>;
}

impl AuthRequestExt for Request {
    fn auth_user(&self) -> Result<&AuthUser, AnalysisError> {
        self.extensions()
            .get::<AuthUser>()
            .ok_or_else(|| AnalysisError::auth("Authentication required"))
    }

    fn optional_auth_user(&self) -> Option<&AuthUser> {
        self.extensions().get::<AuthUser>()
    }
}

async fn validate_api_key(
    api_key: &str,
    state: &Arc<AppState>,
    correlation_id: &str,
) -> Result<AuthUser, AuthError> {
    // Validate API key format
    if !api_key.starts_with("ak_") || api_key.len() < 12 {
        return Err(AuthError {
            error_type: ErrorType::Authentication,
            message: "Invalid API key format".to_string(),
            status_code: StatusCode::UNAUTHORIZED,
            correlation_id: correlation_id.to_string(),
        });
    }

    // Extract prefix for efficient lookup
    let key_prefix = &api_key[3..11];

    // Validate against database
    if let Some(pool) = &state.spanner_pool {
        let spanner_conn = pool.get().await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to get database connection: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?;
        // Use RBAC query manager for comprehensive API key validation
        let gcp_settings = GcpSettings {
            project_id: std::env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "episteme-dev".to_string()),
            spanner_instance: std::env::var("SPANNER_INSTANCE_ID").unwrap_or_else(|_| "episteme-instance".to_string()),
            spanner_database: std::env::var("SPANNER_DATABASE_ID").unwrap_or_else(|_| "analysis_engine".to_string()),
            storage_bucket: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
            storage_bucket_name: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
            pubsub_topic: std::env::var("PUBSUB_TOPIC").unwrap_or_else(|_| "analysis-events".to_string()),
            region: std::env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string()),
        };
        let spanner_client = Arc::new(create_spanner_client(&gcp_settings).await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to create Spanner client: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?);
        let redis_client = Arc::new(redis::Client::open("redis://127.0.0.1/").map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to create Redis client: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?);
        let rbac_manager = get_rbac_query_manager(spanner_client, redis_client);
        match rbac_manager.validate_api_key_with_permissions(api_key, key_prefix, &spanner_conn).await {
            Ok(user) => {
                // Log successful authentication
                audit_auth_success(state, &user.user_id, AuthMethod::ApiKey).await;
                
                // Convert to AuthUser and set correct auth method
                let mut auth_user = convert_user_to_auth_user(user);
                auth_user.auth_method = AuthMethod::ApiKey;
                Ok(auth_user)
            }
            Err(e) => {
                // Log failed authentication
                audit_auth_failure(state, None, AuthMethod::ApiKey, &e.to_string()).await;

                Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: e.to_string(),
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.to_string(),
                })
            }
        }
    } else {
        // Fallback for testing without database
        if api_key == "ak_test_key_12345678" {
            // Create a test user with basic permissions
            #[cfg(feature = "security-storage")]
            let test_permissions = vec![
                crate::models::security::Permission {
                    id: "read_permission".to_string(),
                    name: "read_permission".to_string(),
                    action: "read".to_string(),
                    resource: "analysis".to_string(),
                    effect: crate::models::security::PermissionEffect::Allow,
                    conditions: None,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                },
                crate::models::security::Permission {
                    id: "create_permission".to_string(),
                    name: "create_permission".to_string(),
                    action: "create".to_string(),
                    resource: "analysis".to_string(),
                    effect: crate::models::security::PermissionEffect::Allow,
                    conditions: None,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                },
            ];
            
            #[cfg(not(feature = "security-storage"))]
            let test_permissions = vec!["analysis:read".to_string(), "analysis:create".to_string()];
            
            Ok(AuthUser {
                user_id: "test-user".to_string(),
                rate_limit: 100,
                auth_method: AuthMethod::ApiKey,
                roles: vec!["user".to_string()],
                permissions: test_permissions,
                scopes: vec!["analysis:read".to_string(), "analysis:write".to_string()],
            })
        } else {
            Err(AuthError {
                error_type: ErrorType::Authentication,
                message: "Invalid API key".to_string(),
                status_code: StatusCode::UNAUTHORIZED,
                correlation_id: correlation_id.to_string(),
            })
        }
    }
}

async fn validate_jwt_token(
    token: &str,
    state: &Arc<AppState>,
    correlation_id: &str,
    req: &Request,
) -> Result<AuthUser, AuthError> {
    // Get JWT secret from environment
    let jwt_secret = std::env::var("JWT_SECRET").map_err(|_| AuthError {
        error_type: ErrorType::Internal,
        message: "JWT_SECRET not configured".to_string(),
        status_code: StatusCode::INTERNAL_SERVER_ERROR,
        correlation_id: correlation_id.to_string(),
    })?;

    // Decode header to determine algorithm and key ID
    let header = decode_header(token).map_err(|e| AuthError {
        error_type: ErrorType::Authentication,
        message: format!("Invalid JWT header: {e}"),
        status_code: StatusCode::UNAUTHORIZED,
        correlation_id: correlation_id.to_string(),
    })?;
    
    // First try RSA key manager for RS256 tokens
    let rsa_manager = get_rsa_key_manager();
    let rsa_keys = rsa_manager.read().await;
    
    // Try RSA keys first if key ID is present
    if let Some(kid) = &header.kid {
        if let Some(rsa_key) = rsa_keys.get_verification_key(kid) {
            // Use RS256 with RSA key
            let mut validation = Validation::new(Algorithm::RS256);
            let expected_aud = std::env::var("JWT_AUDIENCE").unwrap_or_else(|_| "ccl-analysis-engine".to_string());
            let expected_iss = std::env::var("JWT_ISSUER").unwrap_or_else(|_| "ccl-platform".to_string());
            validation.set_audience(&[&expected_aud]);
            validation.set_issuer(&[&expected_iss]);
            validation.validate_exp = true;
            validation.validate_nbf = true;
            validation.validate_aud = true;
            
            let token_data = decode::<Claims>(token, &rsa_key.public_key, &validation)
                .map_err(|e| {
                    let message = match e.kind() {
                        jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired",
                        jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format",
                        jsonwebtoken::errors::ErrorKind::InvalidAudience => "Invalid token audience",
                        jsonwebtoken::errors::ErrorKind::InvalidSignature => "Invalid token signature",
                        _ => "Token validation failed",
                    };
                    
                    AuthError {
                        error_type: ErrorType::Authentication,
                        message: message.to_string(),
                        status_code: StatusCode::UNAUTHORIZED,
                        correlation_id: correlation_id.to_string(),
                    }
                })?;
            
            let claims = &token_data.claims;
            
            // Continue with existing validation logic...
            return validate_claims_and_user(claims, state, correlation_id, req).await.map(convert_user_to_auth_user);
        }
    }
    
    // Fallback to HS256 for backward compatibility
    drop(rsa_keys); // Release read lock
    tracing::warn!("Falling back to HS256 JWT validation in auth_extractor - consider migrating to RS256");
    
    // Set up validation for HS256
    let mut validation = Validation::new(Algorithm::HS256);
    let expected_aud =
        std::env::var("JWT_AUDIENCE").unwrap_or_else(|_| "ccl-analysis-engine".to_string());
    let expected_iss =
        std::env::var("JWT_ISSUER").unwrap_or_else(|_| "ccl-platform".to_string());
    validation.set_audience(&[&expected_aud]);
    validation.set_issuer(&[&expected_iss]);
    validation.validate_exp = true;
    validation.validate_nbf = true;
    validation.validate_aud = true;

    // Decode and validate token with HS256
    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(jwt_secret.as_bytes()),
        &validation,
    )
    .map_err(|e| {
        let message = match e.kind() {
            jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired",
            jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format",
            jsonwebtoken::errors::ErrorKind::InvalidAudience => "Invalid token audience",
            jsonwebtoken::errors::ErrorKind::InvalidSignature => "Invalid token signature",
            _ => "Token validation failed",
        };

        AuthError {
            error_type: ErrorType::Authentication,
            message: message.to_string(),
            status_code: StatusCode::UNAUTHORIZED,
            correlation_id: correlation_id.to_string(),
        }
    })?;

    let claims = &token_data.claims;

    // Check token revocation
    if let Some(jti) = &claims.jti {
        if is_token_revoked(jti).await {
            return Err(AuthError {
                error_type: ErrorType::Authentication,
                message: "Token has been revoked".to_string(),
                status_code: StatusCode::UNAUTHORIZED,
                correlation_id: correlation_id.to_string(),
            });
        }
    }

    // Validate device binding if configured
    if std::env::var("JWT_REQUIRE_DEVICE_BINDING").unwrap_or_default() == "true" {
        if let Some(device_id) = &claims.device_id {
            let current_device = generate_device_fingerprint(req);
            if device_id != &current_device {
                return Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: "Device mismatch".to_string(),
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.to_string(),
                });
            }
        }
    }

    // Try to load user with permissions from database
    let user = if let Some(pool) = &state.spanner_pool {
        let spanner_conn = pool.get().await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to get database connection: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?;
        
        let gcp_settings = GcpSettings {
            project_id: std::env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "episteme-dev".to_string()),
            spanner_instance: std::env::var("SPANNER_INSTANCE_ID").unwrap_or_else(|_| "episteme-instance".to_string()),
            spanner_database: std::env::var("SPANNER_DATABASE_ID").unwrap_or_else(|_| "analysis_engine".to_string()),
            storage_bucket: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
            storage_bucket_name: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
            pubsub_topic: std::env::var("PUBSUB_TOPIC").unwrap_or_else(|_| "analysis-events".to_string()),
            region: std::env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string()),
        };
        let spanner_client = Arc::new(create_spanner_client(&gcp_settings).await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to create Spanner client: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?);
        let redis_client = Arc::new(redis::Client::open("redis://127.0.0.1/").map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to create Redis client: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?);
        let rbac_manager = get_rbac_query_manager(spanner_client, redis_client);
        match rbac_manager.get_user_with_permissions(&claims.sub, &spanner_conn).await {
            Ok(user) => {
                let mut auth_user = convert_user_to_auth_user(user);
                auth_user.auth_method = AuthMethod::JwtToken;
                auth_user.scopes = claims.scope.as_ref()
                    .map(|s| s.split_whitespace().map(|s| s.to_string()).collect())
                    .unwrap_or_default();
                auth_user
            },
            Err(_) => {
                // Fallback to basic user if RBAC lookup fails
                let rate_limit = get_user_rate_limit(&claims.sub, state).await.unwrap_or(100);
                AuthUser {
                    user_id: claims.sub.clone(),
                    rate_limit,
                    auth_method: AuthMethod::JwtToken,
                    roles: vec![],
                    permissions: vec![],
                    scopes: claims.scope.as_ref()
                        .map(|s| s.split_whitespace().map(|s| s.to_string()).collect())
                        .unwrap_or_default(),
                }
            }
        }
    } else {
        // No database available - use basic user
        let rate_limit = 100;
        AuthUser {
            user_id: claims.sub.clone(),
            rate_limit,
            auth_method: AuthMethod::JwtToken,
            roles: vec![],
            permissions: vec![],
            scopes: claims.scope.as_ref()
                .map(|s| s.split_whitespace().map(|s| s.to_string()).collect())
                .unwrap_or_default(),
        }
    };

    // Log successful authentication
    audit_auth_success(state, &user.user_id, AuthMethod::JwtToken).await;

    Ok(user)
}

async fn validate_jwt_token_owned(
    token: String,
    state: &Arc<AppState>,
    correlation_id: &str,
    device_fingerprint: String,
) -> Result<AuthUser, AuthError> {
    // Get JWT secret from environment
    let jwt_secret = std::env::var("JWT_SECRET").map_err(|_| AuthError {
        error_type: ErrorType::Internal,
        message: "JWT_SECRET not configured".to_string(),
        status_code: StatusCode::INTERNAL_SERVER_ERROR,
        correlation_id: correlation_id.to_string(),
    })?;

    // Set up validation
    let mut validation = Validation::new(Algorithm::HS256);
    let expected_aud =
        std::env::var("JWT_AUDIENCE").unwrap_or_else(|_| "ccl-analysis-engine".to_string());
    let expected_iss =
        std::env::var("JWT_ISSUER").unwrap_or_else(|_| "ccl-platform".to_string());
    validation.set_audience(&[&expected_aud]);
    validation.set_issuer(&[&expected_iss]);
    validation.validate_exp = true;
    validation.validate_nbf = true;
    validation.validate_aud = true;

    // Decode and validate token
    let token_data = decode::<Claims>(
        &token,
        &DecodingKey::from_secret(jwt_secret.as_bytes()),
        &validation,
    )
    .map_err(|e| {
        let message = match e.kind() {
            jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired",
            jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format",
            jsonwebtoken::errors::ErrorKind::InvalidAudience => "Invalid token audience",
            jsonwebtoken::errors::ErrorKind::InvalidSignature => "Invalid token signature",
            _ => "Token validation failed",
        };

        AuthError {
            error_type: ErrorType::Authentication,
            message: message.to_string(),
            status_code: StatusCode::UNAUTHORIZED,
            correlation_id: correlation_id.to_string(),
        }
    })?;

    let claims = &token_data.claims;

    // Check token revocation
    if let Some(jti) = &claims.jti {
        if is_token_revoked(jti).await {
            return Err(AuthError {
                error_type: ErrorType::Authentication,
                message: "Token has been revoked".to_string(),
                status_code: StatusCode::UNAUTHORIZED,
                correlation_id: correlation_id.to_string(),
            });
        }
    }

    // Validate device binding if configured
    if std::env::var("JWT_REQUIRE_DEVICE_BINDING").unwrap_or_default() == "true" {
        if let Some(device_id) = &claims.device_id {
            if device_id != &device_fingerprint {
                return Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: "Device mismatch".to_string(),
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.to_string(),
                });
            }
        }
    }

    // Try to load user with permissions from database
    let user = if let Some(pool) = &state.spanner_pool {
        let spanner_conn = pool.get().await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to get database connection: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?;
        
        let gcp_settings = GcpSettings {
            project_id: std::env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "episteme-dev".to_string()),
            spanner_instance: std::env::var("SPANNER_INSTANCE_ID").unwrap_or_else(|_| "episteme-instance".to_string()),
            spanner_database: std::env::var("SPANNER_DATABASE_ID").unwrap_or_else(|_| "analysis_engine".to_string()),
            storage_bucket: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
            storage_bucket_name: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
            pubsub_topic: std::env::var("PUBSUB_TOPIC").unwrap_or_else(|_| "analysis-events".to_string()),
            region: std::env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string()),
        };
        let spanner_client = Arc::new(create_spanner_client(&gcp_settings).await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to create Spanner client: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?);
        let redis_client = Arc::new(redis::Client::open("redis://127.0.0.1/").map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to create Redis client: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?);
        let rbac_manager = get_rbac_query_manager(spanner_client, redis_client);
        match rbac_manager.get_user_with_permissions(&claims.sub, &spanner_conn).await {
            Ok(user) => {
                let mut auth_user = convert_user_to_auth_user(user);
                auth_user.auth_method = AuthMethod::JwtToken;
                auth_user.scopes = claims.scope.as_ref()
                    .map(|s| s.split_whitespace().map(|s| s.to_string()).collect())
                    .unwrap_or_default();
                auth_user
            },
            Err(_) => {
                // Fallback to basic user if RBAC lookup fails
                let rate_limit = get_user_rate_limit(&claims.sub, state).await.unwrap_or(100);
                AuthUser {
                    user_id: claims.sub.clone(),
                    rate_limit,
                    auth_method: AuthMethod::JwtToken,
                    roles: vec![],
                    permissions: vec![],
                    scopes: claims.scope.as_ref()
                        .map(|s| s.split_whitespace().map(|s| s.to_string()).collect())
                        .unwrap_or_default(),
                }
            }
        }
    } else {
        // No database available - use basic user
        let rate_limit = 100;
        AuthUser {
            user_id: claims.sub.clone(),
            rate_limit,
            auth_method: AuthMethod::JwtToken,
            roles: vec![],
            permissions: vec![],
            scopes: claims.scope.as_ref()
                .map(|s| s.split_whitespace().map(|s| s.to_string()).collect())
                .unwrap_or_default(),
        }
    };

    // Log successful authentication
    audit_auth_success(state, &user.user_id, AuthMethod::JwtToken).await;

    Ok(user)
}

// API key validation functions moved to RBAC query manager

async fn get_user_rate_limit(user_id: &str, state: &Arc<AppState>) -> Option<i64> {
    if let Some(pool) = &state.spanner_pool {
        let spanner = pool.get().await.ok()?;
        let mut statement = google_cloud_spanner::statement::Statement::new(
            "SELECT rate_limit FROM users WHERE user_id = @user_id",
        );
        statement.add_param("user_id", &user_id);

        if let Ok(mut transaction) = spanner.read_only_transaction().await {
            if let Ok(mut reader) = transaction.query(statement).await {
                if let Ok(Some(row)) = reader.next().await {
                    if let Ok(rate_limit) = row.column_by_name::<i64>("rate_limit") {
                        return Some(rate_limit);
                    }
                }
            }
        }
    }
    None
}

fn generate_device_fingerprint(req: &Request) -> String {
    use sha2::{Digest, Sha256};

    let mut hasher = Sha256::new();
    let headers = req.headers();

    // Add user agent
    if let Some(user_agent) = headers.get("user-agent") {
        hasher.update(user_agent.as_bytes());
    }

    // Add accept headers
    if let Some(accept) = headers.get("accept") {
        hasher.update(accept.as_bytes());
    }
    if let Some(accept_lang) = headers.get("accept-language") {
        hasher.update(accept_lang.as_bytes());
    }
    if let Some(accept_enc) = headers.get("accept-encoding") {
        hasher.update(accept_enc.as_bytes());
    }

    format!("{:x}", hasher.finalize())
}

async fn is_token_revoked(_jti: &str) -> bool {
    // In production, check against Redis or database
    // For now, return false
    false
}

/// Convert rbac_queries::User to AuthUser
fn convert_user_to_auth_user(user: crate::api::middleware::rbac_queries::User) -> AuthUser {
    let rate_limit = 1000; // Default rate limit
    
    #[cfg(feature = "security-storage")]
    let permissions = user.permissions;
    #[cfg(not(feature = "security-storage"))]
    let permissions = user.permissions.iter().map(|p| format!("{}:{}", p.action, p.resource)).collect();
    
    AuthUser {
        user_id: user.user_id,
        rate_limit,
        auth_method: match user.auth_method {
            crate::api::middleware::rbac_queries::AuthMethod::JwtToken => AuthMethod::JwtToken,
            crate::api::middleware::rbac_queries::AuthMethod::ApiKey => AuthMethod::ApiKey,
            crate::api::middleware::rbac_queries::AuthMethod::Basic => AuthMethod::Unknown,
        },
        roles: user.roles,
        permissions,
        scopes: user.scopes,
    }
}

async fn validate_claims_and_user(
    claims: &Claims,
    state: &Arc<AppState>,
    correlation_id: &str,
    _req: &Request<axum::body::Body>,
) -> Result<User, AuthError> {
    // Get database connection
    if let Some(pool) = &state.spanner_pool {
        let spanner_conn = pool.get().await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to get database connection: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?;
        
        let gcp_settings = GcpSettings {
            project_id: std::env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "episteme-dev".to_string()),
            spanner_instance: std::env::var("SPANNER_INSTANCE_ID").unwrap_or_else(|_| "episteme-instance".to_string()),
            spanner_database: std::env::var("SPANNER_DATABASE_ID").unwrap_or_else(|_| "analysis_engine".to_string()),
            storage_bucket: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
            storage_bucket_name: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
            pubsub_topic: std::env::var("PUBSUB_TOPIC").unwrap_or_else(|_| "analysis-events".to_string()),
            region: std::env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string()),
        };
        let spanner_client = Arc::new(create_spanner_client(&gcp_settings).await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to create Spanner client: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?);
        let redis_client = Arc::new(redis::Client::open("redis://127.0.0.1/").map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to create Redis client: {e:?}"),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?);
        let rbac_manager = get_rbac_query_manager(spanner_client, redis_client);
        match rbac_manager.get_user_with_permissions(&claims.sub, &spanner_conn).await {
            Ok(mut user) => {
                let user_id = user.user_id.clone();
                user.auth_method = crate::api::middleware::rbac_queries::AuthMethod::JwtToken;
                
                // Log successful authentication
                audit_auth_success(state, &user_id, AuthMethod::JwtToken).await;
                Ok(user)
            }
            Err(e) => {
                warn!("Failed to get user with permissions: {}", e);
                Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: "User not found or inactive".to_string(),
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.to_string(),
                })
            }
        }
    } else {
        Err(AuthError {
            error_type: ErrorType::Internal,
            message: "Database unavailable".to_string(),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })
    }
}

async fn audit_auth_success(state: &Arc<AppState>, user_id: &str, auth_method: AuthMethod) {
    let audit_logger = AuditLogger::new(state.spanner_pool.clone());
    let event = AuditEventBuilder::new(AuditAction::LoginSuccess)
        .user_id(user_id.to_string())
        .outcome(AuditOutcome::Success)
        .severity(AuditSeverity::Info)
        .metadata(serde_json::json!({
            "auth_method": format!("{:?}", auth_method),
        }))
        .build();

    if let Err(e) = audit_logger.log_event(event).await {
        tracing::error!("Failed to log auth success: {}", e);
    }
}

async fn audit_auth_failure(
    state: &Arc<AppState>,
    user_id: Option<&str>,
    auth_method: AuthMethod,
    error: &str,
) {
    let audit_logger = AuditLogger::new(state.spanner_pool.clone());
    let mut builder = AuditEventBuilder::new(AuditAction::LoginFailure)
        .outcome(AuditOutcome::Failure)
        .severity(AuditSeverity::Warning)
        .metadata(serde_json::json!({
            "auth_method": format!("{:?}", auth_method),
            "error": error,
        }));

    if let Some(uid) = user_id {
        builder = builder.user_id(uid.to_string());
    }

    let event = builder.build();

    if let Err(e) = audit_logger.log_event(event).await {
        tracing::error!("Failed to log auth failure: {}", e);
    }
}

// ==================== Tower Service Implementation ====================

/// Tower Service for optional authentication middleware
#[derive(Clone)]
pub struct OptionalAuthService<S> {
    inner: S,
    state: Arc<AppState>,
}

impl<S> Service<Request> for OptionalAuthService<S>
where
    S: Service<Request, Response = Response> + Send + 'static + Clone,
    S::Future: Send + 'static,
    S::Error: Into<Box<dyn std::error::Error + Send + Sync>> + Send + 'static,
{
    type Response = Response;
    type Error = S::Error;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, mut req: Request) -> Self::Future {
        let state = self.state.clone();
        let mut inner = self.inner.clone();

        Box::pin(async move {
            let correlation_id = Uuid::new_v4().to_string();

            // Extract auth headers before async operations
            let api_key = req
                .headers()
                .get("x-api-key")
                .and_then(|v| v.to_str().ok())
                .map(|s| s.to_string());
            let auth_header = req
                .headers()
                .get(header::AUTHORIZATION)
                .and_then(|v| v.to_str().ok())
                .map(|s| s.to_string());

            // Extract device fingerprint data for JWT validation
            let device_fingerprint = generate_device_fingerprint(&req);

            // Try to authenticate but don't fail if not present
            if let Some(api_key) = api_key {
                if let Ok(user) = validate_api_key(&api_key, &state, &correlation_id).await {
                    req.extensions_mut().insert(user);
                }
            } else if let Some(auth_header) = auth_header {
                if let Some(token) = auth_header.strip_prefix("Bearer ") {
                    if let Ok(user) = validate_jwt_token_owned(
                        token.to_string(),
                        &state,
                        &correlation_id,
                        device_fingerprint,
                    )
                    .await
                    {
                        req.extensions_mut().insert(user);
                    }
                }
            }

            // Call the inner service
            inner.call(req).await
        })
    }
}

/// Tower Layer for optional authentication
#[derive(Clone)]
pub struct OptionalAuthLayer {
    state: Arc<AppState>,
}

impl OptionalAuthLayer {
    pub fn new(state: Arc<AppState>) -> Self {
        Self { state }
    }
}

impl<S> Layer<S> for OptionalAuthLayer {
    type Service = OptionalAuthService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        OptionalAuthService {
            inner,
            state: self.state.clone(),
        }
    }
}

/// Tower Service for mandatory authentication middleware
#[derive(Clone)]
pub struct AuthService<S> {
    inner: S,
    state: Arc<AppState>,
}

impl<S> Service<Request> for AuthService<S>
where
    S: Service<Request, Response = Response> + Send + 'static + Clone,
    S::Future: Send + 'static,
    S::Error: Into<Box<dyn std::error::Error + Send + Sync>> + Send + 'static,
{
    type Response = Response;
    type Error = S::Error;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, mut req: Request) -> Self::Future {
        let state = self.state.clone();
        let mut inner = self.inner.clone();

        Box::pin(async move {
            let correlation_id = Uuid::new_v4().to_string();

            // Extract auth headers before async operations
            let api_key = req
                .headers()
                .get("x-api-key")
                .and_then(|v| v.to_str().ok())
                .map(|s| s.to_string());
            let auth_header = req
                .headers()
                .get(header::AUTHORIZATION)
                .and_then(|v| v.to_str().ok())
                .map(|s| s.to_string());

            // Extract device fingerprint data for JWT validation
            let device_fingerprint = generate_device_fingerprint(&req);

            // Try to authenticate - fail if not present
            let auth_result = if let Some(api_key) = api_key {
                validate_api_key(&api_key, &state, &correlation_id).await
            } else if let Some(auth_header) = auth_header {
                if let Some(token) = auth_header.strip_prefix("Bearer ") {
                    validate_jwt_token_owned(
                        token.to_string(),
                        &state,
                        &correlation_id,
                        device_fingerprint,
                    )
                    .await
                } else {
                    Err(AuthError {
                        error_type: ErrorType::Authentication,
                        message: "Invalid authorization header format".to_string(),
                        status_code: StatusCode::UNAUTHORIZED,
                        correlation_id: correlation_id.clone(),
                    })
                }
            } else {
                Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: "No authentication provided".to_string(),
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.clone(),
                })
            };

            match auth_result {
                Ok(auth_user) => {
                    // Insert authenticated user into request extensions
                    req.extensions_mut().insert(auth_user);
                    // Call the inner service
                    inner.call(req).await
                }
                Err(auth_error) => {
                    // Log authentication failure
                    audit_auth_failure(&state, None, AuthMethod::Unknown, &auth_error.message)
                        .await;
                    // Convert AuthError to Response and return
                    let response = auth_error.into_response();
                    Ok(response)
                }
            }
        })
    }
}

/// Tower Layer for mandatory authentication
#[derive(Clone)]
pub struct AuthLayer {
    state: Arc<AppState>,
}

impl AuthLayer {
    pub fn new(state: Arc<AppState>) -> Self {
        Self { state }
    }
}

impl<S> Layer<S> for AuthLayer {
    type Service = AuthService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        AuthService {
            inner,
            state: self.state.clone(),
        }
    }
}
