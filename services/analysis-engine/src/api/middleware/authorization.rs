//! Authorization middleware for Role-Based Access Control (RBAC)
//!
//! This module provides comprehensive authorization middleware that integrates
//! with the existing authentication system to enforce permission-based access control.

use crate::api::{
    auth_extractor::{AuthUser, AuthMethod},
    errors::{ErrorResponse, ErrorType},
    AppState,
};

#[cfg(feature = "security-storage")]
use crate::models::security::{Permission, PermissionEffect};

#[cfg(not(feature = "security-storage"))]
use super::rbac_queries::{Permission};

#[cfg(not(feature = "security-storage"))]
#[derive(Debug, Clone, PartialEq)]
pub enum PermissionEffect {
    Allow,
    Deny,
}

use super::rbac_queries::get_rbac_query_manager;
use axum::{
    extract::Request,
    http::{Method, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
};
use std::sync::Arc;
use crate::storage::gcp_clients::create_spanner_client;
use crate::config::GcpSettings;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Required permission for accessing an endpoint
#[derive(Debug, Clone, PartialEq)]
pub struct RequiredPermission {
    pub action: String,
    pub resource: String,
    pub context: Option<PermissionContext>,
}

/// Context for permission evaluation
#[derive(Debug, Clone, PartialEq)]
pub struct PermissionContext {
    pub resource_id: Option<String>,
    pub ownership: OwnershipType,
    pub conditions: Vec<String>,
}

/// Types of resource ownership for access control
#[derive(Debug, Clone, PartialEq)]
pub enum OwnershipType {
    Own,          // User owns the resource
    Team,         // User's team owns the resource
    Organization, // User's organization owns the resource
    Any,          // No ownership restriction
}

/// Configuration for authorization middleware
#[derive(Debug, Clone)]
pub struct AuthorizationConfig {
    pub enabled: bool,
    pub enforce_permissions: bool,
    pub log_authorization_decisions: bool,
    pub cache_permissions: bool,
    pub fallback_to_legacy: bool,
}

impl Default for AuthorizationConfig {
    fn default() -> Self {
        Self {
            enabled: std::env::var("RBAC_ENABLED").unwrap_or_default() == "true",
            enforce_permissions: std::env::var("RBAC_ENFORCE").unwrap_or_default() == "true",
            log_authorization_decisions: std::env::var("RBAC_LOG_DECISIONS").unwrap_or_default() == "true",
            cache_permissions: std::env::var("RBAC_CACHE_PERMISSIONS").unwrap_or_default() != "false",
            fallback_to_legacy: std::env::var("RBAC_FALLBACK").unwrap_or_default() == "true",
        }
    }
}

/// Authorization middleware that checks user permissions for endpoints
pub async fn authorization_middleware(
    req: Request,
    next: Next,
) -> Response {
    let correlation_id = Uuid::new_v4().to_string();
    
    // Get authorization configuration
    let config = AuthorizationConfig::default();
    
    // Skip authorization if disabled
    if !config.enabled {
        debug!("Authorization middleware disabled, proceeding");
        return next.run(req).await;
    }

    // Get authenticated user from request extensions
    let auth_user = match req.extensions().get::<AuthUser>() {
        Some(user) => user.clone(),
        None => {
            // No authenticated user - this should have been caught by auth middleware
            error!("Authorization middleware called without authenticated user");
            return create_auth_error(
                "Authentication required for authorization check",
                StatusCode::UNAUTHORIZED,
                &correlation_id,
            ).into_response();
        }
    };

    // Get application state
    let state = match req.extensions().get::<Arc<AppState>>() {
        Some(state) => state.clone(),
        None => {
            error!("Application state not found in request extensions");
            return create_internal_error(
                "Application state not available",
                &correlation_id,
            ).into_response();
        }
    };

    // Extract the endpoint path and method for permission checking
    let path = req.uri().path();
    let method = req.method();
    
    // Check if user has permission for this operation
    match check_endpoint_permission(&auth_user, path, method, &state, &config, &correlation_id).await {
        Ok(AuthorizationResult::Allowed) => {
            // Permission granted - proceed to next handler
            if config.log_authorization_decisions {
                info!(
                    "Authorization granted: user={}, method={}, path={}, correlation_id={}",
                    auth_user.user_id, method, path, correlation_id
                );
            }
            next.run(req).await
        },
        Ok(AuthorizationResult::Denied(reason)) => {
            // Permission denied
            warn!(
                "Authorization denied: user={}, method={}, path={}, reason={}, correlation_id={}",
                auth_user.user_id, method, path, reason, correlation_id
            );
            
            if config.enforce_permissions {
                // Return 403 Forbidden
                create_forbidden_error(&reason, &correlation_id).into_response()
            } else {
                // Log only mode - allow request but log denial
                warn!("RBAC in log-only mode: would have denied access");
                next.run(req).await
            }
        },
        Ok(AuthorizationResult::Fallback) => {
            // Fallback to legacy authorization
            if config.fallback_to_legacy {
                debug!("Falling back to legacy authorization for user {}", auth_user.user_id);
                next.run(req).await
            } else {
                // No fallback allowed
                create_forbidden_error(
                    "Access denied - authorization system unavailable",
                    &correlation_id,
                ).into_response()
            }
        },
        Err(e) => {
            // Authorization check failed
            error!(
                "Authorization check failed: user={}, method={}, path={}, error={}, correlation_id={}",
                auth_user.user_id, method, path, e, correlation_id
            );
            
            if config.fallback_to_legacy {
                warn!("Authorization error, falling back to legacy mode");
                next.run(req).await
            } else {
                create_internal_error(
                    "Authorization check failed",
                    &correlation_id,
                ).into_response()
            }
        }
    }
}

/// Result of authorization check
#[derive(Debug)]
pub enum AuthorizationResult {
    Allowed,
    Denied(String),
    Fallback,
}

/// Check if user has permission for a specific endpoint
async fn check_endpoint_permission(
    user: &AuthUser,
    path: &str,
    method: &Method,
    state: &Arc<AppState>,
    _config: &AuthorizationConfig,
    correlation_id: &str,
) -> Result<AuthorizationResult, Box<dyn std::error::Error + Send + Sync>> {
    // Map endpoint to required permission
    let required_permission = match map_endpoint_to_permission(path, method) {
        Ok(perm) => perm,
        Err(e) => {
            debug!("Unknown endpoint: {} {}, error: {}", method, path, e);
            // For unknown endpoints, use a permissive approach in development
            if cfg!(debug_assertions) {
                return Ok(AuthorizationResult::Allowed);
            } else {
                return Ok(AuthorizationResult::Denied(format!("Unknown endpoint: {} {}", method, path)));
            }
        }
    };
    
    debug!(
        "Checking permission: user={}, action={}, resource={}, correlation_id={}",
        user.user_id, required_permission.action, required_permission.resource, correlation_id
    );
    
    // Check if user has the required permission
    match has_permission(user, &required_permission, state).await {
        Ok(true) => Ok(AuthorizationResult::Allowed),
        Ok(false) => Ok(AuthorizationResult::Denied(format!(
            "Insufficient permissions: requires {}:{}", 
            required_permission.action, 
            required_permission.resource
        ))),
        Err(e) => {
            warn!("Permission check error: {}", e);
            Ok(AuthorizationResult::Fallback)
        }
    }
}

/// Map HTTP endpoint to required permission
fn map_endpoint_to_permission(
    path: &str,
    method: &Method,
) -> Result<RequiredPermission, Box<dyn std::error::Error + Send + Sync>> {
    
    match (method, path) {
        // ============================================================================
        // ANALYSIS ENDPOINTS
        // ============================================================================
        
        // Single file analysis
        (&Method::POST, "/api/v1/analyze") => Ok(RequiredPermission {
            action: "create".to_string(),
            resource: "analysis".to_string(),
            context: None,
        }),
        
        // Flattened analysis endpoints
        (&Method::POST, "/api/v1/analyze/flattened") => Ok(RequiredPermission {
            action: "create".to_string(),
            resource: "analysis".to_string(),
            context: None,
        }),
        (&Method::POST, "/api/v1/analyze/flattened/batch") => Ok(RequiredPermission {
            action: "create".to_string(),
            resource: "analysis".to_string(),
            context: None,
        }),
        
        // Repository analysis
        (&Method::POST, "/api/v1/analyze/repository") => Ok(RequiredPermission {
            action: "create".to_string(),
            resource: "analysis".to_string(),
            context: None,
        }),
        
        // Analysis management
        (&Method::POST, "/api/v1/analysis") => Ok(RequiredPermission {
            action: "create".to_string(),
            resource: "analysis".to_string(),
            context: None,
        }),
        (&Method::GET, "/api/v1/analysis") => Ok(RequiredPermission {
            action: "read".to_string(),
            resource: "analysis".to_string(),
            context: Some(PermissionContext {
                resource_id: None,
                ownership: OwnershipType::Own,
                conditions: vec![],
            }),
        }),
        
        // Specific analysis operations
        (&Method::GET, path) if path.starts_with("/api/v1/analysis/") => {
            let analysis_id = extract_id_from_path(path, "/api/v1/analysis/")?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "analysis".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(analysis_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        (&Method::DELETE, path) if path.starts_with("/api/v1/analysis/") => {
            let analysis_id = extract_id_from_path(path, "/api/v1/analysis/")?;
            Ok(RequiredPermission {
                action: "delete".to_string(),
                resource: "analysis".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(analysis_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        
        // Analysis results and status (specific patterns)
        (&Method::GET, path) if path.contains("/results") => {
            let analysis_id = extract_analysis_id_from_results_path(path)?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "analysis".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(analysis_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        (&Method::GET, path) if path.contains("/status") => {
            let analysis_id = extract_analysis_id_from_status_path(path)?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "analysis".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(analysis_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        (&Method::GET, path) if path.contains("/download") => {
            let analysis_id = extract_analysis_id_from_download_path(path)?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "analysis".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(analysis_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        (&Method::GET, path) if path.contains("/metrics") => {
            let analysis_id = extract_analysis_id_from_metrics_path(path)?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "analysis".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(analysis_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        (&Method::GET, path) if path.contains("/patterns") => {
            let analysis_id = extract_analysis_id_from_patterns_path(path)?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "analysis".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(analysis_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        
        // ============================================================================
        // SECURITY ENDPOINTS
        // ============================================================================
        
        (&Method::POST, "/api/v1/security/scan") => Ok(RequiredPermission {
            action: "execute".to_string(),
            resource: "security_scan".to_string(),
            context: None,
        }),
        
        // ============================================================================
        // STREAMING ENDPOINTS
        // ============================================================================
        
        (&Method::POST, "/api/v1/stream/analyze") => Ok(RequiredPermission {
            action: "create".to_string(),
            resource: "analysis".to_string(),
            context: None,
        }),
        (&Method::GET, path) if path.starts_with("/api/v1/stream/status/") => {
            let stream_id = extract_id_from_path(path, "/api/v1/stream/status/")?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "stream".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(stream_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        (&Method::DELETE, path) if path.starts_with("/api/v1/stream/") => {
            let stream_id = extract_id_from_path(path, "/api/v1/stream/")?;
            Ok(RequiredPermission {
                action: "delete".to_string(),
                resource: "stream".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(stream_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        
        // ============================================================================
        // WEBSOCKET ENDPOINTS
        // ============================================================================
        
        (&Method::GET, path) if path.starts_with("/ws/analysis/") => {
            let analysis_id = extract_id_from_path(path, "/ws/analysis/")?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "analysis".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(analysis_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        (&Method::GET, path) if path.starts_with("/api/v1/stream/progress/") => {
            let stream_id = extract_id_from_path(path, "/api/v1/stream/progress/")?;
            Ok(RequiredPermission {
                action: "read".to_string(),
                resource: "stream".to_string(),
                context: Some(PermissionContext {
                    resource_id: Some(stream_id),
                    ownership: OwnershipType::Own,
                    conditions: vec![],
                }),
            })
        },
        
        // ============================================================================
        // PUBLIC ENDPOINTS (NO AUTHORIZATION REQUIRED)
        // ============================================================================
        
        // Health endpoints
        (&Method::GET, "/health") |
        (&Method::GET, "/health/live") |
        (&Method::GET, "/health/auth") |
        (&Method::GET, "/health/detailed") |
        (&Method::GET, "/health/ready") => Ok(RequiredPermission {
            action: "read".to_string(),
            resource: "public".to_string(),
            context: None,
        }),
        
        // Monitoring endpoints
        (&Method::GET, "/metrics") |
        (&Method::GET, "/backpressure") |
        (&Method::GET, "/circuit-breakers") => Ok(RequiredPermission {
            action: "read".to_string(),
            resource: "public".to_string(),
            context: None,
        }),
        
        // Security monitoring (public)
        (&Method::GET, "/security/stats") |
        (&Method::GET, "/security/csrf-status") => Ok(RequiredPermission {
            action: "read".to_string(),
            resource: "public".to_string(),
            context: None,
        }),
        
        // CSRF token endpoint
        (&Method::GET, "/api/v1/csrf-token") => Ok(RequiredPermission {
            action: "read".to_string(),
            resource: "public".to_string(),
            context: None,
        }),
        
        // Public API endpoints
        (&Method::GET, "/api/v1/languages") |
        (&Method::GET, "/api/v1/version") => Ok(RequiredPermission {
            action: "read".to_string(),
            resource: "public".to_string(),
            context: None,
        }),
        
        // ============================================================================
        // ADMIN ENDPOINTS (FUTURE)
        // ============================================================================
        
        (_, path) if path.starts_with("/admin/") => Ok(RequiredPermission {
            action: "*".to_string(),
            resource: "admin".to_string(),
            context: None,
        }),
        
        // ============================================================================
        // DEFAULT: UNKNOWN ENDPOINT
        // ============================================================================
        
        _ => Err(format!("Unknown endpoint: {} {}", method, path).into()),
    }
}

/// Check if user has the required permission
async fn has_permission(
    user: &AuthUser,
    required: &RequiredPermission,
    state: &Arc<AppState>,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    // 1. Check for wildcard admin permissions
    #[cfg(feature = "security-storage")]
    if user.permissions.iter().any(|p| p.action == "*" && p.resource == "*") {
        debug!("User {} has admin wildcard permission", user.user_id);
        return Ok(true);
    }
    #[cfg(not(feature = "security-storage"))]
    if user.permissions.iter().any(|p| p == "*:*") {
        debug!("User {} has admin wildcard permission", user.user_id);
        return Ok(true);
    }
    
    // 2. Special handling for public resources
    if required.resource == "public" {
        debug!("Allowing access to public resource for user {}", user.user_id);
        return Ok(true);
    }
    
    // 3. Check for exact permission match
    for permission in &user.permissions {
        #[cfg(feature = "security-storage")]
        let matches = permission_matches(permission, required)?;
        #[cfg(not(feature = "security-storage"))]
        let matches = permission_matches_string(permission, required)?;
        
        if matches {
            #[cfg(feature = "security-storage")]
            debug!(
                "Permission match found: user={}, permission={}:{}", 
                user.user_id, permission.action, permission.resource
            );
            #[cfg(not(feature = "security-storage"))]
            debug!(
                "Permission match found: user={}, permission={}", 
                user.user_id, permission
            );
            
            // 4. Evaluate context conditions if present
            if let Some(context) = &required.context {
                // Create a Permission struct from the string for the function call
                let perm = Permission {
                    id: permission.id.clone(),
                    name: permission.name.clone(),
                    resource: required.resource.clone(),
                    action: required.action.clone(),
                    effect: permission.effect.clone(),
                    conditions: permission.conditions.clone(),
                    created_at: permission.created_at,
                    updated_at: permission.updated_at,
                };
                return evaluate_context_conditions(user, &perm, context, state).await;
            }
            return Ok(true);
        }
    }
    
    // 5. Check OAuth2 scopes if applicable
    if user.auth_method == AuthMethod::JwtToken {
        return check_oauth2_scopes(user, required).await;
    }
    
    debug!("No matching permissions found for user {}", user.user_id);
    Ok(false)
}

/// Check if permission string matches requirement (for non-security-storage mode)
#[cfg(not(feature = "security-storage"))]
fn permission_matches_string(
    permission: &str,
    required: &RequiredPermission,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    // Parse permission string format: "action:resource"
    if let Some((action, resource)) = permission.split_once(':') {
        let action_match = action == "*" || action == required.action;
        let resource_match = resource == "*" || resource == required.resource;
        Ok(action_match && resource_match)
    } else {
        // Simple permission string
        Ok(permission == "*" || permission == format!("{}:{}", required.action, required.resource))
    }
}

/// Check if permission matches requirement
#[cfg(feature = "security-storage")]
fn permission_matches(
    permission: &Permission,
    required: &RequiredPermission,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    // Only consider Allow permissions
    if permission.effect != PermissionEffect::Allow {
        return Ok(false);
    }
    
    // Action matching (supports wildcards)
    let action_match = permission.action == "*" || 
                      permission.action == required.action ||
                      wildcard_match(&permission.action, &required.action);
    
    // Resource matching (supports wildcards and hierarchies)
    let resource_match = permission.resource == "*" ||
                        permission.resource == required.resource ||
                        resource_hierarchy_match(&permission.resource, &required.resource);
    
    Ok(action_match && resource_match)
}

/// Evaluate context-specific conditions
async fn evaluate_context_conditions(
    user: &AuthUser,
    _permission: &Permission,
    context: &PermissionContext,
    state: &Arc<AppState>,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    match context.ownership {
        OwnershipType::Own => {
            // Check if user owns the resource
            check_resource_ownership(user, context, state).await
        },
        OwnershipType::Team => {
            // Check if user's team owns the resource
            check_team_ownership(user, context, state).await
        },
        OwnershipType::Organization => {
            // Check if user's organization owns the resource
            check_organization_ownership(user, context, state).await
        },
        OwnershipType::Any => Ok(true),
    }
}

/// Check if user owns the specific resource
async fn check_resource_ownership(
    user: &AuthUser,
    context: &PermissionContext,
    state: &Arc<AppState>,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    if let Some(resource_id) = &context.resource_id {
        // Use RBAC query manager for optimized database access
        if let Some(pool) = &state.spanner_pool {
            let _spanner = pool.get().await.map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("Spanner connection error: {e:?}"))) as Box<dyn std::error::Error + Send + Sync>)?;
            let gcp_settings = GcpSettings {
                project_id: std::env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "episteme-dev".to_string()),
                spanner_instance: std::env::var("SPANNER_INSTANCE_ID").unwrap_or_else(|_| "episteme-instance".to_string()),
                spanner_database: std::env::var("SPANNER_DATABASE_ID").unwrap_or_else(|_| "analysis_engine".to_string()),
                storage_bucket: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
                storage_bucket_name: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
                pubsub_topic: std::env::var("PUBSUB_TOPIC").unwrap_or_else(|_| "analysis-events".to_string()),
                region: std::env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string()),
            };
            let spanner_client = Arc::new(create_spanner_client(&gcp_settings).await.map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("Failed to create Spanner client: {e:?}"))) as Box<dyn std::error::Error + Send + Sync>)?);
            let redis_client = Arc::new(redis::Client::open("redis://127.0.0.1/").map_err(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>)?);
            let rbac_manager = get_rbac_query_manager(spanner_client, redis_client);
            
            // Determine resource type from context (default to "analysis")
            let _resource_type = "analysis"; // Could be extracted from context in future
            
            match rbac_manager.check_resource_ownership(
                &user.user_id,
                resource_id,
            ).await {
                Ok(owns_resource) => return Ok(owns_resource),
                Err(e) => {
                    error!("Failed to check resource ownership: {}", e);
                    // Fail securely - deny access if we can't verify ownership
                    return Ok(false);
                }
            }
        }
        
        // If we can't verify ownership, deny access
        return Ok(false);
    }
    
    // No specific resource ID - allow for general operations
    Ok(true)
}

/// Check if user's team owns the resource
async fn check_team_ownership(
    user: &AuthUser,
    context: &PermissionContext,
    state: &Arc<AppState>,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    if let Some(resource_id) = &context.resource_id {
        if let Some(pool) = &state.spanner_pool {
            let spanner = pool.get().await.map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("Spanner connection error: {e:?}"))) as Box<dyn std::error::Error + Send + Sync>)?;
            let gcp_settings = GcpSettings {
                project_id: std::env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "episteme-dev".to_string()),
                spanner_instance: std::env::var("SPANNER_INSTANCE_ID").unwrap_or_else(|_| "episteme-instance".to_string()),
                spanner_database: std::env::var("SPANNER_DATABASE_ID").unwrap_or_else(|_| "analysis_engine".to_string()),
                storage_bucket: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
                storage_bucket_name: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
                pubsub_topic: std::env::var("PUBSUB_TOPIC").unwrap_or_else(|_| "analysis-events".to_string()),
                region: std::env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string()),
            };
            let spanner_client = Arc::new(create_spanner_client(&gcp_settings).await.map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("Failed to create Spanner client: {e:?}"))) as Box<dyn std::error::Error + Send + Sync>)?);
            let redis_client = Arc::new(redis::Client::open("redis://127.0.0.1/").map_err(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>)?);
            let rbac_manager = get_rbac_query_manager(spanner_client, redis_client);
            
            // Determine resource type from context (default to "analysis")
            let _resource_type = "analysis";
            
            match rbac_manager.check_team_ownership(
                resource_id,  // team_id
                &user.user_id,
                &spanner,
            ).await {
                Ok(team_owns) => return Ok(team_owns),
                Err(e) => {
                    error!("Failed to check team ownership: {}", e);
                    return Ok(false);
                }
            }
        }
    }
    
    Ok(false)
}

/// Check if user's organization owns the resource
async fn check_organization_ownership(
    user: &AuthUser,
    context: &PermissionContext,
    state: &Arc<AppState>,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    if let Some(resource_id) = &context.resource_id {
        if let Some(pool) = &state.spanner_pool {
            let spanner = pool.get().await.map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("Spanner connection error: {e:?}"))) as Box<dyn std::error::Error + Send + Sync>)?;
            let gcp_settings = GcpSettings {
                project_id: std::env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "episteme-dev".to_string()),
                spanner_instance: std::env::var("SPANNER_INSTANCE_ID").unwrap_or_else(|_| "episteme-instance".to_string()),
                spanner_database: std::env::var("SPANNER_DATABASE_ID").unwrap_or_else(|_| "analysis_engine".to_string()),
                storage_bucket: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
                storage_bucket_name: std::env::var("STORAGE_BUCKET_NAME").unwrap_or_else(|_| "episteme-storage".to_string()),
                pubsub_topic: std::env::var("PUBSUB_TOPIC").unwrap_or_else(|_| "analysis-events".to_string()),
                region: std::env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string()),
            };
            let spanner_client = Arc::new(create_spanner_client(&gcp_settings).await.map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("Failed to create Spanner client: {e:?}"))) as Box<dyn std::error::Error + Send + Sync>)?);
            let redis_client = Arc::new(redis::Client::open("redis://127.0.0.1/").map_err(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>)?);
            let rbac_manager = get_rbac_query_manager(spanner_client, redis_client);
            
            // Determine resource type from context (default to "analysis")
            let _resource_type = "analysis";
            
            match rbac_manager.check_organization_ownership(
                resource_id,  // org_id
                &user.user_id,
                &spanner,
            ).await {
                Ok(org_owns) => return Ok(org_owns),
                Err(e) => {
                    error!("Failed to check organization ownership: {}", e);
                    return Ok(false);
                }
            }
        }
    }
    
    Ok(false)
}

/// Check OAuth2 scopes for permission
async fn check_oauth2_scopes(
    user: &AuthUser,
    required: &RequiredPermission,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    // Map required permission to OAuth2 scopes
    let required_scopes = map_permission_to_oauth2_scopes(required);
    
    // Check if user has any of the required scopes
    for required_scope in required_scopes {
        if user.scopes.contains(&required_scope) {
            debug!("User {} has required OAuth2 scope: {}", user.user_id, required_scope);
            return Ok(true);
        }
    }
    
    debug!("User {} missing required OAuth2 scopes", user.user_id);
    Ok(false)
}

/// Map permission to OAuth2 scopes
fn map_permission_to_oauth2_scopes(required: &RequiredPermission) -> Vec<String> {
    let mut scopes = Vec::new();
    
    match (required.action.as_str(), required.resource.as_str()) {
        ("read", "analysis") => {
            scopes.push("analysis:read".to_string());
            scopes.push("analysis:read:all".to_string()); // Admin scope
        },
        ("create", "analysis") => {
            scopes.push("analysis:write".to_string());
        },
        ("update", "analysis") => {
            scopes.push("analysis:write".to_string());
        },
        ("delete", "analysis") => {
            scopes.push("analysis:delete".to_string());
        },
        ("execute", "security_scan") => {
            scopes.push("security:scan".to_string());
        },
        ("*", "*") => {
            scopes.push("admin".to_string());
        },
        _ => {
            // Unknown permission - no matching scopes
        }
    }
    
    scopes
}

/// Simple wildcard matching for actions
#[allow(dead_code)]
fn wildcard_match(pattern: &str, value: &str) -> bool {
    if pattern.contains('*') {
        // Simple wildcard support - could be enhanced with regex
        let pattern_parts: Vec<&str> = pattern.split('*').collect();
        if pattern_parts.len() == 2 {
            let prefix = pattern_parts[0];
            let suffix = pattern_parts[1];
            return value.starts_with(prefix) && value.ends_with(suffix);
        }
    }
    false
}

/// Resource hierarchy matching (e.g., "analysis" matches "analysis:own")
#[allow(dead_code)]
fn resource_hierarchy_match(pattern: &str, value: &str) -> bool {
    // Check if pattern is a parent of value in resource hierarchy
    value.starts_with(pattern) && (value.len() == pattern.len() || value.chars().nth(pattern.len()) == Some(':'))
}

/// Extract ID from URL path
fn extract_id_from_path(path: &str, prefix: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    if let Some(id_part) = path.strip_prefix(prefix) {
        // Extract just the ID (before any additional path segments)
        let id = id_part.split('/').next().unwrap_or(id_part);
        if !id.is_empty() {
            return Ok(id.to_string());
        }
    }
    Err(format!("Could not extract ID from path: {}", path).into())
}

/// Extract analysis ID from results path
fn extract_analysis_id_from_results_path(path: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    // Pattern: /api/v1/analysis/{id}/results
    if let Some(captures) = regex::Regex::new(r"/api/v1/analysis/([^/]+)/results")?.captures(path) {
        if let Some(id) = captures.get(1) {
            return Ok(id.as_str().to_string());
        }
    }
    Err(format!("Could not extract analysis ID from results path: {}", path).into())
}

/// Extract analysis ID from status path
fn extract_analysis_id_from_status_path(path: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    // Pattern: /api/v1/analysis/{id}/status
    if let Some(captures) = regex::Regex::new(r"/api/v1/analysis/([^/]+)/status")?.captures(path) {
        if let Some(id) = captures.get(1) {
            return Ok(id.as_str().to_string());
        }
    }
    Err(format!("Could not extract analysis ID from status path: {}", path).into())
}

/// Extract analysis ID from download path
fn extract_analysis_id_from_download_path(path: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    // Pattern: /api/v1/analysis/{id}/download
    if let Some(captures) = regex::Regex::new(r"/api/v1/analysis/([^/]+)/download")?.captures(path) {
        if let Some(id) = captures.get(1) {
            return Ok(id.as_str().to_string());
        }
    }
    Err(format!("Could not extract analysis ID from download path: {}", path).into())
}

/// Extract analysis ID from metrics path
fn extract_analysis_id_from_metrics_path(path: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    // Pattern: /api/v1/analysis/{id}/metrics
    if let Some(captures) = regex::Regex::new(r"/api/v1/analysis/([^/]+)/metrics")?.captures(path) {
        if let Some(id) = captures.get(1) {
            return Ok(id.as_str().to_string());
        }
    }
    Err(format!("Could not extract analysis ID from metrics path: {}", path).into())
}

/// Extract analysis ID from patterns path
fn extract_analysis_id_from_patterns_path(path: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    // Pattern: /api/v1/analysis/{id}/patterns
    if let Some(captures) = regex::Regex::new(r"/api/v1/analysis/([^/]+)/patterns")?.captures(path) {
        if let Some(id) = captures.get(1) {
            return Ok(id.as_str().to_string());
        }
    }
    Err(format!("Could not extract analysis ID from patterns path: {}", path).into())
}

/// Create authentication error response
fn create_auth_error(
    message: &str,
    _status_code: StatusCode,
    correlation_id: &str,
) -> ErrorResponse {
    let mut error_response = ErrorResponse::new(ErrorType::Authentication, message.to_string());
    error_response.error_code = Some("AUTHORIZATION_REQUIRED".to_string());
    error_response.user_message = Some("Authentication required for this operation.".to_string());
    error_response.correlation_id = Some(correlation_id.to_string());
    error_response
}

/// Create forbidden error response
fn create_forbidden_error(
    message: &str,
    correlation_id: &str,
) -> ErrorResponse {
    let mut error_response = ErrorResponse::new(ErrorType::Authorization, message.to_string());
    error_response.error_code = Some("INSUFFICIENT_PERMISSIONS".to_string());
    error_response.user_message = Some("You do not have permission to perform this operation.".to_string());
    error_response.correlation_id = Some(correlation_id.to_string());
    error_response
}

/// Create internal error response
fn create_internal_error(
    message: &str,
    correlation_id: &str,
) -> ErrorResponse {
    let mut error_response = ErrorResponse::new(ErrorType::Internal, message.to_string());
    error_response.error_code = Some("AUTHORIZATION_ERROR".to_string());
    error_response.user_message = Some("An error occurred during authorization check.".to_string());
    error_response.correlation_id = Some(correlation_id.to_string());
    error_response
}

#[cfg(test)]
mod tests {
    use super::*;
    // Test utils archived - see archive/production-cleanup-*/development-infrastructure/

    #[test]
    fn test_endpoint_mapping() {
        // Test basic analysis endpoint
        let perm = map_endpoint_to_permission("/api/v1/analyze", &Method::POST).unwrap();
        assert_eq!(perm.action, "create");
        assert_eq!(perm.resource, "analysis");
        
        // Test specific analysis endpoint
        let perm = map_endpoint_to_permission("/api/v1/analysis/123", &Method::GET).unwrap();
        assert_eq!(perm.action, "read");
        assert_eq!(perm.resource, "analysis");
        assert!(perm.context.is_some());
        
        // Test public endpoint
        let perm = map_endpoint_to_permission("/health", &Method::GET).unwrap();
        assert_eq!(perm.resource, "public");
    }

    // Tests archived - see archive/production-cleanup-*/development-infrastructure/

    #[test]
    fn test_id_extraction() {
        assert_eq!(
            extract_id_from_path("/api/v1/analysis/123", "/api/v1/analysis/").unwrap(),
            "123"
        );
        
        assert_eq!(
            extract_id_from_path("/api/v1/analysis/abc-def-456/status", "/api/v1/analysis/").unwrap(),
            "abc-def-456"
        );
    }

    #[test]
    fn test_oauth2_scope_mapping() {
        let required = RequiredPermission {
            action: "read".to_string(),
            resource: "analysis".to_string(),
            context: None,
        };
        
        let scopes = map_permission_to_oauth2_scopes(&required);
        assert!(scopes.contains(&"analysis:read".to_string()));
    }
}