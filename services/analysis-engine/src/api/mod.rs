pub mod auth_extractor;
pub mod errors;
pub mod handlers;
pub mod jwks;
pub mod metrics_handler;
pub mod middleware;
pub mod oauth2;
pub mod openapi;
pub mod rate_limit_extractor;
pub mod rbac_health;
pub mod response;
pub mod security_layer;
pub mod security_middleware;
pub mod validation;
pub mod websocket;

#[cfg(feature = "security-storage")]
pub mod encryption_metrics;

use crate::storage::{cache::CacheWarmingConfig, StorageOperations};

use crate::backpressure::{BackpressureConfig, BackpressureManager, BackpressureMetrics};
use crate::circuit_breaker::CircuitBreakerManager;
use crate::config::ServiceConfig;
use crate::models::{AnalysisStatus, ProgressUpdate};
use crate::parser::TreeSitterParser;
use crate::services::analyzer::AnalysisService;
use crate::startup::StartupManager;
use crate::storage::{
    connection_pool::{RedisConnectionManager, RedisPool, SpannerConnectionManager, SpannerPool},
    gcp_clients, CacheManager, PubSubOperations, RedisClient,
};
use bb8::Pool;
use dashmap::DashMap;
use std::sync::Arc;
use tokio::sync::broadcast;

#[derive(Clone)]
pub struct AppState {
    pub spanner_pool: Option<Arc<SpannerPool>>,
    pub storage: Arc<StorageOperations>,
    pub pubsub: Arc<PubSubOperations>,
    pub redis_pool: Option<Arc<RedisPool>>,
    pub cache: Arc<CacheManager>,
    pub config: Arc<ServiceConfig>,
    pub analysis_service: Arc<AnalysisService>,
    pub active_analyses: Arc<DashMap<String, AnalysisStatus>>,
    pub progress_broadcast: broadcast::Sender<ProgressUpdate>,
    pub backpressure_manager: Arc<BackpressureManager>,
    pub circuit_breaker_manager: Arc<CircuitBreakerManager>,
    pub validation_config: crate::validation::ValidationConfig,
    pub contract_validator: Option<Arc<crate::validation::contract_compliance::ContractValidator>>,
    pub streaming_handler:
        Option<Arc<crate::api::handlers::streaming_analysis::StreamingAnalysisHandler>>,
    pub streaming_config: Arc<crate::models::streaming::StreamingConfig>,
    pub startup_manager: Arc<StartupManager>,
    pub parser: Arc<TreeSitterParser>,
}

impl AppState {
    pub async fn new() -> Result<Self, anyhow::Error> {
        use crate::startup::{retry_optional_service, retry_with_backoff, RetryConfig};

        let config = Arc::new(ServiceConfig::from_env()?);
        let startup_manager = Arc::new(StartupManager::new());

        tracing::info!("Starting service initialization with retry logic");

        // Initialize Spanner with retry
        let spanner_pool = retry_with_backoff(
            "Spanner",
            &startup_manager,
            RetryConfig::default(),
            || async {
                let spanner_manager = SpannerConnectionManager::new(config.gcp.clone());
                let pool = Pool::builder().build(spanner_manager).await?;
                Ok(Arc::new(pool))
            },
        )
        .await?;
        let spanner_pool = Some(spanner_pool);

        // Initialize Redis with retry (optional service)
        let redis_pool = retry_optional_service(
            "Redis",
            &startup_manager,
            RetryConfig {
                max_attempts: 5, // Fewer attempts for optional service
                ..Default::default()
            },
            || async {
                let manager = RedisConnectionManager::new(config.redis.clone());
                let pool = Pool::builder().build(manager).await?;
                tracing::info!("Redis connection pool created successfully");
                Ok(Arc::new(pool))
            },
        )
        .await;

        // Initialize Google Cloud Storage with retry
        let storage = retry_with_backoff(
            "Google Cloud Storage",
            &startup_manager,
            RetryConfig::default(),
            || async {
                let storage_client = gcp_clients::create_storage_client(&config.gcp).await?;
                let storage_ops = StorageOperations::new(storage_client).await?;
                Ok(Arc::new(storage_ops))
            },
        )
        .await?;

        // Initialize Google Cloud Pub/Sub with retry
        let pubsub = retry_with_backoff(
            "Google Cloud Pub/Sub",
            &startup_manager,
            RetryConfig::default(),
            || async {
                let pubsub_client = gcp_clients::create_pubsub_client(&config.gcp).await?;
                let pubsub_ops = PubSubOperations::new(pubsub_client).await?;
                Ok(Arc::new(pubsub_ops))
            },
        )
        .await?;

        // Log successful initialization status
        let final_status = startup_manager.get_status().await;
        tracing::info!(
            "Service initialization complete: {} services ready in {:.2}s",
            final_status.ready_services,
            final_status.startup_duration.as_secs_f64()
        );

        // Create cache manager with Redis client
        let redis_client = if redis_pool.is_some() {
            match RedisClient::new().await {
                Ok(client) => Some(Arc::new(client)),
                Err(e) => {
                    tracing::warn!(
                        "Failed to create Redis client: {}. Continuing without Redis.",
                        e
                    );
                    None
                }
            }
        } else {
            None
        };
        let cache = Arc::new(CacheManager::new_with_config(
            redis_client,
            CacheWarmingConfig::default(),
        ));

        // Create broadcast channel for progress updates
        // Buffer size of 1000 should handle high-frequency updates
        let (progress_broadcast, _) = broadcast::channel(1000);

        // Create backpressure manager
        let backpressure_config = BackpressureConfig {
            max_concurrent_analyses: config.analysis.max_concurrent_analyses,
            max_concurrent_parsing: config.analysis.max_concurrent_analyses * 4, // 4x for file parsing
            max_concurrent_database: 100,
            max_concurrent_storage: 150,
            max_analysis_memory_mb: 3000,
            cpu_threshold_percent: 80.0,
            queue_size_threshold: 1000,
            circuit_breaker_failure_threshold: 5,
            circuit_breaker_timeout: std::time::Duration::from_secs(30),
        };
        let backpressure_manager = Arc::new(BackpressureManager::new(backpressure_config));

        // Create analysis service with the operation wrappers
        let mut analysis_service = AnalysisService::new(
            spanner_pool.clone(),
            storage.clone(),
            pubsub.clone(),
            cache.clone(),
            config.clone(),
        )
        .await?;

        // Set the backpressure manager on the analysis service
        analysis_service.set_backpressure_manager(backpressure_manager.clone());
        let analysis_service = Arc::new(analysis_service);

        // Start background task to monitor system metrics
        let bp_manager_clone = backpressure_manager.clone();
        tokio::spawn(async move {
            Self::monitor_system_metrics(bp_manager_clone).await;
        });

        let circuit_breaker_manager =
            Arc::new(CircuitBreakerManager::new(config.circuit_breaker.clone()));

        // Initialize validation configuration
        let validation_config = crate::validation::ValidationConfig::default();
        let contract_validator = if validation_config.enabled {
            Some(Arc::new(
                crate::validation::contract_compliance::ContractValidator::new(
                    validation_config.clone(),
                ),
            ))
        } else {
            None
        };

        // Initialize streaming handler
        let streaming_config = crate::models::streaming::StreamingConfig::default();
        let streaming_handler = Some(Arc::new(
            crate::api::handlers::streaming_analysis::StreamingAnalysisHandler::new(
                streaming_config,
            ),
        ));

        // Initialize parser
        let parser = Arc::new(TreeSitterParser::new(config.clone())?);

        Ok(Self {
            spanner_pool,
            storage,
            pubsub,
            redis_pool,
            cache,
            config,
            analysis_service,
            active_analyses: Arc::new(DashMap::new()),
            progress_broadcast,
            backpressure_manager,
            circuit_breaker_manager,
            validation_config,
            contract_validator,
            streaming_handler,
            streaming_config: Arc::new(crate::models::streaming::StreamingConfig::default()),
            startup_manager,
            parser,
        })
    }

    /// Background task to monitor system metrics and update backpressure manager
    async fn monitor_system_metrics(backpressure_manager: Arc<BackpressureManager>) {
        use crate::metrics::prometheus::update_system_metrics;

        let mut interval = tokio::time::interval(std::time::Duration::from_secs(5));

        loop {
            interval.tick().await;

            // Get current system metrics
            let memory_usage = Self::get_memory_usage_mb().await;
            let cpu_usage = Self::get_cpu_usage_percent().await;

            // Update Prometheus metrics
            let memory_bytes = (memory_usage * 1024 * 1024) as i64;
            update_system_metrics(memory_bytes, cpu_usage as f64);

            let metrics = BackpressureMetrics {
                memory_usage_mb: memory_usage,
                cpu_usage_percent: cpu_usage,
                active_analyses: 0, // This would be updated by the analysis handlers
                active_requests: 0, // Alias for active_analyses for backward compatibility
                queued_requests: 0, // This would be updated by the request queue
                rejected_requests: 0, // This would be tracked by the backpressure manager
                avg_response_time_ms: 0, // This would be calculated from request metrics
                last_updated: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs(),
            };

            if let Err(e) = backpressure_manager.update_metrics(metrics).await {
                tracing::warn!("Failed to update backpressure metrics: {}", e);
            }
        }
    }

    /// Get current memory usage in MB
    async fn get_memory_usage_mb() -> u64 {
        // This is a simplified implementation
        // In production, you'd use a proper system monitoring library
        #[cfg(target_os = "linux")]
        {
            if let Ok(contents) = tokio::fs::read_to_string("/proc/meminfo").await {
                for line in contents.lines() {
                    if line.starts_with("MemAvailable:") {
                        if let Some(value) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = value.parse::<u64>() {
                                let total_mb = 4096; // Assume 4GB total for now
                                return total_mb - (kb / 1024);
                            }
                        }
                    }
                }
            }
        }

        // Fallback: estimate based on process memory
        1024 // Default to 1GB usage
    }

    /// Get current CPU usage percentage
    async fn get_cpu_usage_percent() -> f32 {
        // This is a simplified implementation
        // In production, you'd use a proper system monitoring library
        #[cfg(target_os = "linux")]
        {
            if let Ok(contents) = tokio::fs::read_to_string("/proc/loadavg").await {
                if let Some(load_str) = contents.split_whitespace().next() {
                    if let Ok(load) = load_str.parse::<f32>() {
                        let cpu_count = num_cpus::get() as f32;
                        return (load / cpu_count * 100.0).min(100.0);
                    }
                }
            }
        }

        // Fallback
        25.0 // Default to 25% usage
    }

    /// Get migration router with proper implementation
    pub fn get_migration_router(&self) -> Option<Arc<crate::api::handlers::migration_router::MigrationRouter>> {
        use crate::api::handlers::migration_router::{FeatureFlagService, MigrationRouter};
        
        // Initialize feature flag service with default configuration
        let feature_flags = Arc::new(FeatureFlagService::new());
        
        // Create migration router with proper configuration
        let python_base_url = std::env::var("PYTHON_TRANSFORMER_URL")
            .unwrap_or_else(|_| "http://localhost:8000".to_string());
            
        let router = MigrationRouter::new(feature_flags, python_base_url);
        Some(Arc::new(router))
    }
}
