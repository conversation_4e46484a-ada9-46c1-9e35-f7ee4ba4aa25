//! Data portability export implementation for GDPR Article 20
//!
//! This module handles data exports in machine-readable formats (JSON/CSV),
//! including encrypted field handling, compression, and secure download URLs.

use crate::models::security::{EncryptedField, SecurityError};
use crate::services::security::gdpr::models::*;
use crate::storage::audit::{AuditContext, AuditService};
use crate::storage::encryption::EncryptionService;
use crate::storage::spanner::SpannerOperations;
use anyhow::{Context, Result};
use chrono::{Duration, Utc};
use csv::Writer;
use flate2::write::GzEncoder;
use flate2::Compression;
use google_cloud_spanner::client::Error as SpannerError;
use google_cloud_spanner::statement::Statement;
use serde_json::{json, Value};
use std::collections::HashMap;
use std::io::Write;
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Service for handling GDPR data export requests
pub struct ExportService {
    spanner: Arc<SpannerOperations>,
    encryption: Arc<dyn EncryptionService + Send + Sync>,
    audit: Arc<AuditService>,
    config: GdprConfig,
}

impl ExportService {
    /// Create a new export service
    pub fn new(
        spanner: Arc<SpannerOperations>,
        encryption: Arc<dyn EncryptionService + Send + Sync>,
        audit: Arc<AuditService>,
        config: GdprConfig,
    ) -> Self {
        Self {
            spanner,
            encryption,
            audit,
            config,
        }
    }

    /// Initiate a data export request
    ///
    /// # Arguments
    /// * `user_id` - ID of the user requesting export
    /// * `format` - Desired export format
    /// * `include_encrypted` - Whether to decrypt encrypted fields
    ///
    /// # Returns
    /// * Export request with tracking ID
    pub async fn initiate_export(
        &self,
        user_id: &str,
        format: ExportFormat,
        include_encrypted: bool,
    ) -> Result<ExportRequest> {
        info!("Initiating GDPR export request for user: {}", user_id);

        let request = ExportRequest {
            request_id: Uuid::new_v4().to_string(),
            user_id: user_id.to_string(),
            format,
            include_encrypted,
            requested_at: Utc::now(),
            status: ExportStatus::Pending,
            download_url: None,
            expires_at: None,
        };

        // Store export request
        self.store_export_request(&request).await?;

        // Log audit event
        let context = AuditContext {
            user_id: Some(user_id.to_string()),
            ip_address: None,
            user_agent: None,
            session_id: None,
            request_id: Some(request.request_id.clone()),
        };

        self.audit
            .log_operation_with_metadata(
                "gdpr_export_requested",
                "export_request",
                &request.request_id,
                crate::models::security::AuditResult::Success,
                context,
                Some(HashMap::from([
                    ("format".to_string(), json!(request.format)),
                    ("include_encrypted".to_string(), json!(include_encrypted)),
                ])),
            )
            .await?;

        info!("Export request created: {}", request.request_id);
        Ok(request)
    }

    /// Process a pending export request
    ///
    /// # Arguments
    /// * `request_id` - ID of the export request to process
    ///
    /// # Returns
    /// * Updated export request with download URL
    pub async fn process_export(&self, request_id: &str) -> Result<ExportRequest> {
        info!("Processing export request: {}", request_id);

        // Retrieve export request
        let mut request = self.get_export_request(request_id).await?;

        if request.status != ExportStatus::Pending {
            return Err(anyhow::anyhow!(
                "Export request {} is not in pending status",
                request_id
            ));
        }

        // Update status to processing
        request.status = ExportStatus::Processing;
        self.update_export_request(&request).await?;

        // Generate export data
        match self.generate_export(&request).await {
            Ok((_data, download_url)) => {
                request.status = ExportStatus::Ready;
                request.download_url = Some(download_url);
                request.expires_at =
                    Some(Utc::now() + Duration::hours(self.config.export_expiration_hours as i64));
            }
            Err(e) => {
                error!("Export generation failed for request {}: {}", request_id, e);
                request.status = ExportStatus::Failed;
                self.update_export_request(&request).await?;
                return Err(e);
            }
        }

        // Update request as ready
        self.update_export_request(&request).await?;

        // Log successful export
        let context = AuditContext {
            user_id: Some(request.user_id.clone()),
            ip_address: None,
            user_agent: None,
            session_id: None,
            request_id: Some(request.request_id.clone()),
        };

        self.audit
            .log_operation_with_metadata(
                "gdpr_export_completed",
                "export_request",
                &request.request_id,
                crate::models::security::AuditResult::Success,
                context,
                Some(HashMap::from([
                    ("format".to_string(), json!(request.format)),
                    ("expires_at".to_string(), json!(request.expires_at)),
                ])),
            )
            .await?;

        info!("Export ready for download: {}", request_id);
        Ok(request)
    }

    /// Generate the actual export data
    async fn generate_export(&self, request: &ExportRequest) -> Result<(Vec<u8>, String)> {
        debug!("Generating export for user: {}", request.user_id);

        // Collect all user data
        let export_data = self
            .collect_user_data(&request.user_id, request.include_encrypted)
            .await?;

        // Format data based on requested format
        let formatted_data = match request.format {
            ExportFormat::Json => self.format_as_json(&export_data)?,
            ExportFormat::Csv => self.format_as_csv(&export_data)?,
            ExportFormat::Combined => self.format_as_combined(&export_data)?,
        };

        // Compress data
        let compressed_data = self.compress_data(&formatted_data)?;

        // Generate secure download URL (in production, this would upload to cloud storage)
        let download_url = self.generate_download_url(&request.request_id)?;

        Ok((compressed_data, download_url))
    }

    /// Collect all user data from various tables
    async fn collect_user_data(
        &self,
        user_id: &str,
        include_encrypted: bool,
    ) -> Result<DataExport> {
        let mut export = DataExport {
            metadata: ExportMetadata {
                request_id: Uuid::new_v4().to_string(),
                user_id: user_id.to_string(),
                generated_at: Utc::now(),
                data_from: Utc::now() - Duration::days(365 * 10), // 10 years back
                data_to: Utc::now(),
                format_version: "1.0".to_string(),
                total_records: 0,
            },
            user_data: None,
            analysis_requests: Vec::new(),
            analysis_results: Vec::new(),
            consent_history: Vec::new(),
        };

        // Collect user profile data
        export.user_data = self.export_user_data(user_id, include_encrypted).await?;
        if export.user_data.is_some() {
            export.metadata.total_records += 1;
        }

        // Collect analysis requests
        export.analysis_requests = self
            .export_analysis_requests(user_id, include_encrypted)
            .await?;
        export.metadata.total_records += export.analysis_requests.len() as u64;

        // Collect analysis results
        export.analysis_results = self
            .export_analysis_results(user_id, include_encrypted)
            .await?;
        export.metadata.total_records += export.analysis_results.len() as u64;

        // Collect consent history
        export.consent_history = self.export_consent_history(user_id).await?;
        export.metadata.total_records += export.consent_history.len() as u64;

        Ok(export)
    }

    /// Export user profile data
    async fn export_user_data(
        &self,
        user_id: &str,
        include_encrypted: bool,
    ) -> Result<Option<Value>> {
        let stmt = Statement::new("SELECT * FROM user_data WHERE user_id = @user_id");
        let mut stmt = stmt;
        stmt.add_param("user_id", &user_id.to_string());

        let mut transaction = self.spanner.client.read_only_transaction().await?;
        let mut result_set = transaction.query(stmt).await?;

        if let Some(row) = result_set.next().await? {
            let created_at = {
                let timestamp_str: String = row.column_by_name("created_at")?;
                chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                    .context("Failed to parse created_at timestamp")?
                    .with_timezone(&Utc)
            };
            let updated_at = {
                let timestamp_str: String = row.column_by_name("updated_at")?;
                chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                    .context("Failed to parse updated_at timestamp")?
                    .with_timezone(&Utc)
            };

            let data = json!({
                "user_id": user_id,
                "created_at": created_at,
                "updated_at": updated_at,
            });

            // Handle encrypted fields if requested
            if include_encrypted {
                // Decrypt encrypted fields based on schema
                if let Some(encrypted_notes) = data.get("encrypted_notes") {
                    if let Some(encrypted_str) = encrypted_notes.as_str() {
                        match self.decrypt_field(encrypted_str).await {
                            Ok(decrypted) => {
                                data.insert("notes".to_string(), Value::String(decrypted));
                                data.remove("encrypted_notes");
                            }
                            Err(e) => {
                                tracing::warn!("Failed to decrypt notes for user {}: {}", user_id, e);
                                // Keep encrypted field but mark as undecryptable
                                data.insert("notes_status".to_string(), Value::String("encrypted_undecryptable".to_string()));
                            }
                        }
                    }
                }
                
                // Handle other encrypted fields like email, phone, etc.
                for field_name in ["encrypted_email", "encrypted_phone", "encrypted_address"] {
                    if let Some(encrypted_value) = data.get(field_name) {
                        if let Some(encrypted_str) = encrypted_value.as_str() {
                            match self.decrypt_field(encrypted_str).await {
                                Ok(decrypted) => {
                                    let plain_field = field_name.strip_prefix("encrypted_").unwrap_or(field_name);
                                    data.insert(plain_field.to_string(), Value::String(decrypted));
                                    data.remove(field_name);
                                }
                                Err(e) => {
                                    tracing::warn!("Failed to decrypt {} for user {}: {}", field_name, user_id, e);
                                }
                            }
                        }
                    }
                }
            } else {
                // Remove encrypted fields from export if not requested
                let encrypted_fields = ["encrypted_notes", "encrypted_email", "encrypted_phone", "encrypted_address"];
                for field in encrypted_fields {
                    data.remove(field);
                }
            }

            Ok(Some(data))
        } else {
            Ok(None)
        }
    }

    /// Export analysis requests
    async fn export_analysis_requests(
        &self,
        user_id: &str,
        _include_encrypted: bool,
    ) -> Result<Vec<Value>> {
        let stmt = Statement::new(
            "SELECT * FROM analysis_requests
             WHERE user_id = @user_id
             ORDER BY created_at DESC",
        );
        let mut stmt = stmt;
        stmt.add_param("user_id", &user_id.to_string());

        let mut transaction = self.spanner.client.read_only_transaction().await?;
        let mut result_set = transaction.query(stmt).await?;

        let mut requests = Vec::new();
        while let Some(row) = result_set.next().await? {
            let created_at = {
                let timestamp_str: String = row.column_by_name("created_at")?;
                chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                    .context("Failed to parse created_at timestamp")?
                    .with_timezone(&Utc)
            };

            let data = json!({
                "id": row.column_by_name::<String>("id")?,
                "repository_url": row.column_by_name::<String>("repository_url")?,
                "status": row.column_by_name::<String>("status")?,
                "created_at": created_at,
            });

            requests.push(data);
        }

        Ok(requests)
    }

    /// Export analysis results
    async fn export_analysis_results(
        &self,
        user_id: &str,
        _include_encrypted: bool,
    ) -> Result<Vec<Value>> {
        let stmt = Statement::new(
            "SELECT * FROM analysis_results
             WHERE user_id = @user_id
             ORDER BY created_at DESC",
        );
        let mut stmt = stmt;
        stmt.add_param("user_id", &user_id.to_string());

        let mut transaction = self.spanner.client.read_only_transaction().await?;
        let mut result_set = transaction.query(stmt).await?;

        let mut results = Vec::new();
        while let Some(row) = result_set.next().await? {
            let created_at = {
                let timestamp_str: String = row.column_by_name("created_at")?;
                chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                    .context("Failed to parse created_at timestamp")?
                    .with_timezone(&Utc)
            };

            let data = json!({
                "analysis_id": row.column_by_name::<String>("analysis_id")?,
                "created_at": created_at,
                // Add other fields as needed
            });

            results.push(data);
        }

        Ok(results)
    }

    /// Export consent history
    async fn export_consent_history(&self, user_id: &str) -> Result<Vec<ConsentRecord>> {
        let stmt = Statement::new(
            "SELECT * FROM gdpr_consent_records
             WHERE user_id = @user_id
             ORDER BY timestamp DESC",
        );
        let mut stmt = stmt;
        stmt.add_param("user_id", &user_id.to_string());

        let mut transaction = self.spanner.client.read_only_transaction().await?;
        let mut result_set = transaction.query(stmt).await?;

        let mut records = Vec::new();
        while let Some(row) = result_set.next().await? {
            let consent_type_str: String = row.column_by_name("consent_type")?;
            let metadata_json: Option<String> = row.column_by_name("metadata")?;

            records.push(ConsentRecord {
                consent_id: row.column_by_name("consent_id")?,
                user_id: row.column_by_name("user_id")?,
                consent_type: match consent_type_str.as_str() {
                    "DataProcessing" => ConsentType::DataProcessing,
                    "Analytics" => ConsentType::Analytics,
                    "Marketing" => ConsentType::Marketing,
                    "DataSharing" => ConsentType::DataSharing,
                    "AutomatedDecisionMaking" => ConsentType::AutomatedDecisionMaking,
                    custom => ConsentType::Custom(custom.to_string()),
                },
                granted: row.column_by_name("granted")?,
                timestamp: {
                    let timestamp_str: String = row.column_by_name("timestamp")?;
                    chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                        .context("Failed to parse timestamp")?
                        .with_timezone(&Utc)
                },
                ip_address: row.column_by_name("ip_address")?,
                user_agent: row.column_by_name("user_agent")?,
                consent_version: row.column_by_name("consent_version")?,
                metadata: metadata_json.and_then(|s| serde_json::from_str(&s).ok()),
            });
        }

        Ok(records)
    }

    /// Format export data as JSON
    fn format_as_json(&self, export: &DataExport) -> Result<Vec<u8>> {
        let json_data = serde_json::to_vec_pretty(export)?;
        Ok(json_data)
    }

    /// Format export data as CSV (simplified for tabular data)
    fn format_as_csv(&self, export: &DataExport) -> Result<Vec<u8>> {
        let mut buffer = Vec::new();

        // Export analysis requests as CSV
        if !export.analysis_requests.is_empty() {
            buffer.write_all(b"=== ANALYSIS REQUESTS ===\n")?;

            // Use a block to ensure the CSV writer is dropped before we use buffer again
            {
                let mut wtr = Writer::from_writer(&mut buffer);

                // Write header
                wtr.write_record(&["id", "repository_url", "status", "created_at"])?;

                // Write data
                for request in &export.analysis_requests {
                    if let Some(obj) = request.as_object() {
                        wtr.write_record(&[
                            obj.get("id").and_then(|v| v.as_str()).unwrap_or(""),
                            obj.get("repository_url")
                                .and_then(|v| v.as_str())
                                .unwrap_or(""),
                            obj.get("status").and_then(|v| v.as_str()).unwrap_or(""),
                            obj.get("created_at").and_then(|v| v.as_str()).unwrap_or(""),
                        ])?;
                    }
                }
                wtr.flush()?;
            } // CSV writer is dropped here, releasing the mutable borrow

            buffer.write_all(b"\n\n")?;
        }

        // Export consent history as CSV
        if !export.consent_history.is_empty() {
            buffer.write_all(b"=== CONSENT HISTORY ===\n")?;

            // Use a block to ensure the CSV writer is dropped before we use buffer again
            {
                let mut wtr = Writer::from_writer(&mut buffer);

                // Write header
                wtr.write_record(&[
                    "consent_id",
                    "consent_type",
                    "granted",
                    "timestamp",
                    "version",
                ])?;

                // Write data
                for consent in &export.consent_history {
                    wtr.write_record(&[
                        &consent.consent_id,
                        &format!("{:?}", consent.consent_type),
                        &consent.granted.to_string(),
                        &consent.timestamp.to_rfc3339(),
                        &consent.consent_version,
                    ])?;
                }
                wtr.flush()?;
            } // CSV writer is dropped here, releasing the mutable borrow
        }

        Ok(buffer)
    }

    /// Format export data as combined JSON + CSV in ZIP
    fn format_as_combined(&self, export: &DataExport) -> Result<Vec<u8>> {
        use zip::write::FileOptions;
        use zip::ZipWriter;

        let mut buffer = std::io::Cursor::new(Vec::new());
        let mut zip = ZipWriter::new(&mut buffer);

        // Add JSON file with explicit type parameters to resolve type inference
        zip.start_file::<_, ()>("export.json", FileOptions::default())?;
        let json_data = self.format_as_json(export)?;
        zip.write_all(&json_data)?;

        // Add CSV file with explicit type parameters
        zip.start_file::<_, ()>("export.csv", FileOptions::default())?;
        let csv_data = self.format_as_csv(export)?;
        zip.write_all(&csv_data)?;

        // Add metadata file with explicit type parameters
        zip.start_file::<_, ()>("metadata.json", FileOptions::default())?;
        let metadata = serde_json::to_vec_pretty(&export.metadata)?;
        zip.write_all(&metadata)?;

        zip.finish()?;
        Ok(buffer.into_inner())
    }

    /// Compress data using gzip
    fn compress_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
        encoder.write_all(data)?;
        Ok(encoder.finish()?)
    }

    /// Generate secure download URL
    fn generate_download_url(&self, request_id: &str) -> Result<String> {
        // In production, this would:
        // 1. Upload compressed data to cloud storage
        // 2. Generate a signed URL with expiration
        // 3. Return the secure download link

        // For now, return a placeholder
        Ok(format!("/api/gdpr/export/download/{}", request_id))
    }

    /// Store export request in database
    async fn store_export_request(&self, request: &ExportRequest) -> Result<()> {
        let request = request.clone();
        let (_, _) = self.spanner.client
            .read_write_transaction(|tx| {
                let request = request.clone();
                Box::pin(async move {
                    let stmt = Statement::new(
                        "INSERT INTO gdpr_export_requests
                         (request_id, user_id, format, include_encrypted, requested_at, status)
                         VALUES (@request_id, @user_id, @format, @include_encrypted, @requested_at, @status)"
                    );
                    let mut stmt = stmt;
                    stmt.add_param("request_id", &request.request_id);
                    stmt.add_param("user_id", &request.user_id);
                    stmt.add_param("format", &request.format.to_string());
                    stmt.add_param("include_encrypted", &request.include_encrypted);
                    stmt.add_param("requested_at", &request.requested_at.to_rfc3339());
                    stmt.add_param("status", &request.status.to_string());

                    tx.update(stmt).await?;
                    Ok::<(), SpannerError>(())
                })
            })
            .await?;

        Ok(())
    }

    /// Update export request in database
    async fn update_export_request(&self, request: &ExportRequest) -> Result<()> {
        let request = request.clone();
        let (_, _) = self
            .spanner
            .client
            .read_write_transaction(|tx| {
                let request = request.clone();
                Box::pin(async move {
                    let stmt = Statement::new(
                        "UPDATE gdpr_export_requests
                         SET status = @status,
                             download_url = @download_url,
                             expires_at = @expires_at
                         WHERE request_id = @request_id",
                    );
                    let mut stmt = stmt;
                    stmt.add_param("status", &request.status.to_string());
                    stmt.add_param("download_url", &request.download_url);
                    stmt.add_param("expires_at", &request.expires_at.map(|dt| dt.to_rfc3339()));
                    stmt.add_param("request_id", &request.request_id);

                    tx.update(stmt).await?;
                    Ok::<(), SpannerError>(())
                })
            })
            .await?;

        Ok(())
    }

    /// Get export request from database
    pub async fn get_export_request(&self, request_id: &str) -> Result<ExportRequest> {
        let stmt = Statement::new(
            "SELECT request_id, user_id, format, include_encrypted, requested_at, status, download_url, expires_at
             FROM gdpr_export_requests
             WHERE request_id = @request_id"
        );
        let mut stmt = stmt;
        stmt.add_param("request_id", &request_id.to_string());

        let mut transaction = self.spanner.client.read_only_transaction().await?;
        let mut result_set = transaction.query(stmt).await?;

        if let Some(row) = result_set.next().await? {
            let format_str: String = row.column_by_name("format")?;
            let status_str: String = row.column_by_name("status")?;

            let requested_at = {
                let timestamp_str: String = row.column_by_name("requested_at")?;
                chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                    .context("Failed to parse requested_at timestamp")?
                    .with_timezone(&Utc)
            };
            let expires_at = {
                let timestamp_str: Option<String> = row.column_by_name("expires_at")?;
                timestamp_str
                    .map(|ts| {
                        chrono::DateTime::parse_from_rfc3339(&ts)
                            .context("Failed to parse expires_at timestamp")
                            .map(|dt| dt.with_timezone(&Utc))
                    })
                    .transpose()?
            };

            Ok(ExportRequest {
                request_id: row.column_by_name("request_id")?,
                user_id: row.column_by_name("user_id")?,
                format: match format_str.as_str() {
                    "Json" => ExportFormat::Json,
                    "Csv" => ExportFormat::Csv,
                    "Combined" => ExportFormat::Combined,
                    _ => ExportFormat::Json,
                },
                include_encrypted: row.column_by_name("include_encrypted")?,
                requested_at,
                status: match status_str.as_str() {
                    "Pending" => ExportStatus::Pending,
                    "Processing" => ExportStatus::Processing,
                    "Ready" => ExportStatus::Ready,
                    "Failed" => ExportStatus::Failed,
                    "Expired" => ExportStatus::Expired,
                    _ => ExportStatus::Pending,
                },
                download_url: row.column_by_name("download_url")?,
                expires_at,
            })
        } else {
            Err(anyhow::anyhow!("Export request not found: {}", request_id))
        }
    }

    /// Mark export as downloaded and log audit event
    pub async fn mark_as_downloaded(
        &self,
        request_id: &str,
        ip_address: Option<String>,
    ) -> Result<()> {
        let request = self.get_export_request(request_id).await?;

        // Log download event
        let context = AuditContext {
            user_id: Some(request.user_id.clone()),
            ip_address,
            user_agent: None,
            session_id: None,
            request_id: Some(request.request_id.clone()),
        };

        self.audit
            .log_operation(
                "gdpr_export_downloaded",
                "export_request",
                &request.request_id,
                crate::models::security::AuditResult::Success,
                context,
            )
            .await?;

        Ok(())
    }

    /// Process all pending export requests (for scheduled job)
    pub async fn process_pending_exports(&self) -> Result<Vec<ExportRequest>> {
        info!("Processing pending GDPR export requests");

        // Get pending requests
        let stmt = Statement::new(
            "SELECT request_id
             FROM gdpr_export_requests
             WHERE status = 'Pending'
             ORDER BY requested_at
             LIMIT @batch_size",
        );
        let mut stmt = stmt;
        stmt.add_param("batch_size", &(self.config.batch_size as i64));

        let mut transaction = self.spanner.client.read_only_transaction().await?;
        let mut result_set = transaction.query(stmt).await?;

        let mut request_ids = Vec::new();
        while let Some(row) = result_set.next().await? {
            request_ids.push(row.column_by_name::<String>("request_id")?);
        }

        let mut completed_requests = Vec::new();
        for request_id in request_ids {
            match self.process_export(&request_id).await {
                Ok(request) => completed_requests.push(request),
                Err(e) => {
                    error!("Failed to process export request {}: {}", request_id, e);
                    // Continue processing other requests
                }
            }
        }

        info!("Processed {} export requests", completed_requests.len());
        Ok(completed_requests)
    }

    /// Clean up expired exports
    pub async fn cleanup_expired_exports(&self) -> Result<u64> {
        let (_, count) = self
            .spanner
            .client
            .read_write_transaction(|tx| {
                Box::pin(async move {
                    let stmt = Statement::new(
                        "UPDATE gdpr_export_requests
                         SET status = 'Expired'
                         WHERE status = 'Ready'
                         AND expires_at < @now",
                    );
                    let mut stmt = stmt;
                    stmt.add_param("now", &Utc::now().to_rfc3339());

                    let count = tx.update(stmt).await?;
                    Ok::<u64, SpannerError>(count as u64)
                })
            })
            .await?;

        info!("Marked {} exports as expired", count);
        Ok(count)
    }
}

impl ToString for ExportFormat {
    fn to_string(&self) -> String {
        match self {
            ExportFormat::Json => "Json".to_string(),
            ExportFormat::Csv => "Csv".to_string(),
            ExportFormat::Combined => "Combined".to_string(),
        }
    }
}

impl ToString for ExportStatus {
    fn to_string(&self) -> String {
        match self {
            ExportStatus::Pending => "Pending".to_string(),
            ExportStatus::Processing => "Processing".to_string(),
            ExportStatus::Ready => "Ready".to_string(),
            ExportStatus::Failed => "Failed".to_string(),
            ExportStatus::Expired => "Expired".to_string(),
        }
    }
}

impl ToString for DeletionStatus {
    fn to_string(&self) -> String {
        match self {
            DeletionStatus::Pending => "Pending".to_string(),
            DeletionStatus::Processing => "Processing".to_string(),
            DeletionStatus::Completed => "Completed".to_string(),
            DeletionStatus::Failed => "Failed".to_string(),
            DeletionStatus::Cancelled => "Cancelled".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;
    use serde_json::json;
    use std::collections::HashMap;

    #[test]
    fn test_format_as_json() {
        let export = create_test_export();
        let json_result = serde_json::to_vec_pretty(&export);
        assert!(json_result.is_ok());

        let json_data = json_result.unwrap();
        assert!(!json_data.is_empty());

        // Verify it's valid JSON by parsing it back
        let parsed: Result<DataExport, _> = serde_json::from_slice(&json_data);
        assert!(parsed.is_ok());
    }

    #[test]
    fn test_csv_formatting_with_proper_borrowing() {
        // This test validates that our fix for the mutable borrow issue works
        let mut buffer = Vec::new();

        // First CSV section
        buffer.write_all(b"=== SECTION 1 ===\n").unwrap();
        {
            let mut wtr = csv::Writer::from_writer(&mut buffer);
            wtr.write_record(&["col1", "col2", "col3"]).unwrap();
            wtr.write_record(&["val1", "val2", "val3"]).unwrap();
            wtr.flush().unwrap();
        } // Writer drops here, releasing the borrow

        // This should work without borrow checker errors
        buffer.write_all(b"\n\n").unwrap();

        // Second CSV section
        buffer.write_all(b"=== SECTION 2 ===\n").unwrap();
        {
            let mut wtr = csv::Writer::from_writer(&mut buffer);
            wtr.write_record(&["colA", "colB"]).unwrap();
            wtr.write_record(&["valA", "valB"]).unwrap();
            wtr.flush().unwrap();
        }

        let result = String::from_utf8(buffer).unwrap();
        assert!(result.contains("SECTION 1"));
        assert!(result.contains("SECTION 2"));
        assert!(result.contains("\n\n"));
    }

    #[test]
    fn test_zip_type_inference_fix() {
        use std::io::{Cursor, Write};
        use zip::write::FileOptions;
        use zip::ZipWriter;

        let mut buffer = Cursor::new(Vec::new());
        let mut zip = ZipWriter::new(&mut buffer);

        // This should compile without type inference errors
        zip.start_file::<_, ()>("file1.txt", FileOptions::default())
            .unwrap();
        zip.write_all(b"Content 1").unwrap();

        zip.start_file::<_, ()>("file2.txt", FileOptions::default())
            .unwrap();
        zip.write_all(b"Content 2").unwrap();

        zip.finish().unwrap();

        let result = buffer.into_inner();
        assert!(!result.is_empty());
        assert_eq!(&result[0..2], b"PK"); // ZIP file signature
    }

    #[test]
    fn test_combined_export_format() {
        use std::io::Cursor;
        use zip::read::ZipArchive;

        // Create test data
        let export = create_test_export();
        let json_data = serde_json::to_vec_pretty(&export).unwrap();

        // Create ZIP with explicit type parameters
        let mut buffer = Cursor::new(Vec::new());
        {
            use zip::write::{FileOptions, ZipWriter};
            let mut zip = ZipWriter::new(&mut buffer);

            zip.start_file::<_, ()>("export.json", FileOptions::default())
                .unwrap();
            zip.write_all(&json_data).unwrap();

            zip.start_file::<_, ()>("metadata.json", FileOptions::default())
                .unwrap();
            zip.write_all(b"{}").unwrap();

            zip.finish().unwrap();
        }

        // Verify ZIP structure
        let zip_data = buffer.into_inner();
        let cursor = Cursor::new(zip_data);
        let archive = ZipArchive::new(cursor);

        assert!(archive.is_ok());
        let mut archive = archive.unwrap();
        assert_eq!(archive.len(), 2);
        assert!(archive.by_name("export.json").is_ok());
        assert!(archive.by_name("metadata.json").is_ok());
    }

    // Helper function to create test data
    fn create_test_export() -> DataExport {
        DataExport {
            metadata: ExportMetadata {
                request_id: "test-123".to_string(),
                user_id: "user-456".to_string(),
                generated_at: Utc::now(),
                data_from: Utc::now() - chrono::Duration::days(30),
                data_to: Utc::now(),
                format_version: "1.0".to_string(),
                total_records: 2,
            },
            user_data: Some(json!({
                "user_id": "user-456",
                "email": "<EMAIL>"
            })),
            analysis_requests: vec![json!({
                "id": "req-1",
                "repository_url": "https://github.com/test/repo",
                "status": "completed",
                "created_at": "2024-01-01T00:00:00Z"
            })],
            analysis_results: vec![],
            consent_history: vec![ConsentRecord {
                consent_id: "consent-1".to_string(),
                user_id: "user-456".to_string(),
                consent_type: ConsentType::DataProcessing,
                granted: true,
                timestamp: Utc::now(),
                ip_address: Some("127.0.0.1".to_string()),
                user_agent: Some("Test Agent".to_string()),
                consent_version: "1.0".to_string(),
                metadata: None,
            }],
        }
    }
}
