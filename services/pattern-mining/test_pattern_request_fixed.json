{"repository_id": "repo_a1b2c3d4e5f6g7h8", "analysis_id": "analysis_z9y8x7w6v5u4t3s2", "request_id": "req_12345678abcdef12", "ast_data": {"files": [{"file_path": "test.py", "language": "python", "content_hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_bytes": 100, "ast_nodes": [{"id": "1", "type": "FunctionDef", "name": "test", "range": {"start_line": 1, "end_line": 5, "start_column": 0, "end_column": 15}, "parent_id": "", "children_ids": [], "properties": {}, "text": "def test(): pass", "annotations": []}], "metrics": {"lines_of_code": 5, "complexity": 1, "function_count": 1}, "symbols": [], "imports": [], "code_features": {}}], "repository_metrics": {"total_files": 1, "total_lines": 5, "languages": {"python": {"files": 1, "lines": 5}}}}, "detection_config": {"enabled_detectors": ["code_smells"], "confidence_threshold": 0.5, "language_specific": {}, "performance_limits": {}, "output_preferences": {}}, "context": {"user_id": "", "organization_id": "", "priority": "normal", "callback_url": "", "tags": ["test"]}}