{"repository_id": "repo_a1b2c3d4e5f6g7h8", "analysis_id": "analysis_z9y8x7w6v5u4t3s2", "request_id": "req_12345678abcdef12", "ast_data": {"files": [{"file_path": "bad_code.py", "language": "python", "content_hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_bytes": 500, "ast_nodes": [{"id": "1", "type": "FunctionDef", "name": "bad_function", "range": {"start_line": 1, "end_line": 20, "start_column": 0, "end_column": 50}, "parent_id": "", "children_ids": [], "properties": {}, "text": "def bad_function(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t):\n    if a == 1:\n        if b == 2:\n            if c == 3:\n                if d == 4:\n                    password = 'hardcoded123'\n                    query = 'SELECT * FROM users WHERE id = ' + str(user_id)\n                    for user in users:\n                        for item in user.items:\n                            for detail in item.details:\n                                result += str(detail)\n                    return result\n    return None", "annotations": []}], "metrics": {"lines_of_code": 20, "complexity": 8, "function_count": 1}, "symbols": [], "imports": [], "code_features": {}}], "repository_metrics": {"total_files": 1, "total_lines": 20, "languages": {"python": {"files": 1, "lines": 20}}}}, "detection_config": {"enabled_detectors": ["code_smells", "security_vulnerabilities", "anti_patterns"], "confidence_threshold": 0.5, "language_specific": {}, "performance_limits": {}, "output_preferences": {}}, "context": {"user_id": "", "organization_id": "", "priority": "normal", "callback_url": "", "tags": ["test"]}}