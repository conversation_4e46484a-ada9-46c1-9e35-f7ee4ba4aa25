# Query Intelligence Service Scripts

This directory contains essential production scripts for the Query Intelligence service. All demo, test, and duplicate scripts have been cleaned up.

## 🚀 Production Scripts

### Deployment & Orchestration
- **`deploy_cloud_run_production.sh`** - Deploy service to Google Cloud Run in production
- **`deploy-orchestrator.py`** - Advanced deployment orchestration with rollback capabilities
- **`deployment-monitor.py`** - Monitor deployment status and health

### Service Management
- **`start_production.py`** - Start service in production mode with full validation
- **`start-local.sh`** - Start service locally for development
- **`rollback-automation.sh`** - Automated rollback procedures for failed deployments

### Validation & Testing
- **`validate_production.py`** - ✅ **PRIMARY VALIDATION SCRIPT** - Comprehensive production readiness checks
- **`validate_monitoring_setup.sh`** - Validate monitoring and alerting configuration
- **`validate-startup.sh`** - Validate service startup and health
- **`run_e2e_tests.py`** - End-to-end integration testing

### Monitoring & Operations
- **`setup_production_monitoring.py`** - Configure production monitoring, metrics, and alerting
- **`generate-openapi.py`** - Generate OpenAPI specification for API documentation

## 🎯 Key Scripts for Production Deployment

1. **`validate_production.py`** - Always run this first to ensure production readiness
2. **`deploy_cloud_run_production.sh`** - Deploy to Cloud Run
3. **`setup_production_monitoring.py`** - Set up monitoring after deployment
4. **`validate_monitoring_setup.sh`** - Verify monitoring is working

## 🧹 Cleanup Summary

Removed the following categories of files:
- Demo scripts (`demo_*.py`)
- Duplicate validation scripts
- Development-only test utilities
- Temporary benchmark and analysis scripts
- Python cache directories (`__pycache__`)

## 📝 Usage Examples

```bash
# Validate production readiness
./validate_production.py

# Deploy to production
./deploy_cloud_run_production.sh

# Set up monitoring
./setup_production_monitoring.py

# Run end-to-end tests
./run_e2e_tests.py
```

## 🔧 Maintenance

All scripts are production-ready and actively maintained. Demo and experimental scripts have been removed to keep the directory clean and focused on essential operations.
