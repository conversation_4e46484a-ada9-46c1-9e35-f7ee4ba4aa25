#!/usr/bin/env python3
"""
Production Validation Script for Query Intelligence Service
Validates all required configuration and dependencies before service startup.
"""

import os
import sys
import asyncio
from pathlib import Path
from typing import Dict, List, Tuple

# Add src to path for imports
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Load environment variables from .env file if it exists
def load_env_file():
    """Load environment variables from .env file (don't override existing env vars)"""
    env_file = Path(__file__).parent.parent / ".env"
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Remove quotes if present
                    value = value.strip('"\'')
                    # Only set if not already in environment
                    if key not in os.environ:
                        os.environ[key] = value

load_env_file()

from query_intelligence.config.settings import get_settings
from query_intelligence.clients.redis import get_redis_client
from query_intelligence.clients.analysis_engine import get_analysis_engine_client
from query_intelligence.clients.pattern_mining import get_pattern_mining_client


class ProductionValidator:
    """Validates production environment configuration"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.settings = None
    
    def validate_environment_variables(self) -> bool:
        """Validate required environment variables"""
        print("🔍 Validating environment variables...")
        
        required_vars = [
            "ENVIRONMENT",
            "REDIS_URL",
            "ANALYSIS_ENGINE_URL",
            "PATTERN_MINING_URL",
            "JWT_SECRET_KEY",
            "GOOGLE_API_KEY"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            self.errors.append(f"Missing required environment variables: {', '.join(missing_vars)}")
            return False
        
        print("✅ Environment variables validated")
        return True
    
    def validate_settings(self) -> bool:
        """Validate application settings"""
        print("🔍 Validating application settings...")
        
        try:
            self.settings = get_settings()
            print("✅ Settings loaded successfully")
            return True
        except Exception as e:
            self.errors.append(f"Settings validation failed: {e}")
            return False
    
    def validate_production_config(self) -> bool:
        """Validate production-specific configuration"""
        print("🔍 Validating production configuration...")
        
        if not self.settings:
            self.errors.append("Settings not loaded")
            return False
        
        try:
            # This will raise ValueError if production config is invalid
            self.settings.validate_production_settings()
            print("✅ Production configuration validated")
            return True
        except ValueError as e:
            self.errors.append(f"Production configuration invalid: {e}")
            return False
    
    async def validate_redis_connection(self) -> bool:
        """Validate Redis connection"""
        print("🔍 Validating Redis connection...")
        
        try:
            redis_client = get_redis_client()
            await redis_client.ping()
            print("✅ Redis connection validated")
            return True
        except Exception as e:
            self.errors.append(f"Redis connection failed: {e}")
            return False
    
    async def validate_external_services(self) -> bool:
        """Validate external service connections"""
        print("🔍 Validating external service connections...")
        
        # Validate Analysis Engine
        try:
            analysis_client = get_analysis_engine_client()
            if await analysis_client.health_check():
                print("✅ Analysis Engine connection validated")
            else:
                self.warnings.append("Analysis Engine health check failed")
        except Exception as e:
            self.warnings.append(f"Analysis Engine connection failed: {e}")
        
        # Validate Pattern Mining (optional service)
        try:
            pattern_client = get_pattern_mining_client()
            if await pattern_client.health_check():
                print("✅ Pattern Mining connection validated")
            else:
                print("⚠️  Pattern Mining service unavailable (optional - service has fallbacks)")
        except Exception as e:
            print(f"⚠️  Pattern Mining service unavailable: {e} (optional - service has fallbacks)")
        
        return True
    
    def validate_file_permissions(self) -> bool:
        """Validate file permissions and security"""
        print("🔍 Validating file permissions...")
        
        # Check if running as non-root user
        if os.geteuid() == 0:
            self.warnings.append("Service running as root user - security risk")
        
        # Check log directory permissions
        log_dir = Path("/var/log/query-intelligence")
        if log_dir.exists():
            stat = log_dir.stat()
            if stat.st_mode & 0o777 != 0o755:
                self.warnings.append(f"Log directory has insecure permissions: {oct(stat.st_mode)}")
        
        print("✅ File permissions validated")
        return True
    
    def validate_security_settings(self) -> bool:
        """Validate security configuration"""
        print("🔍 Validating security settings...")
        
        if not self.settings:
            self.errors.append("Settings not loaded")
            return False
        
        # Check JWT secret strength
        if len(self.settings.JWT_SECRET_KEY) < 32:
            self.errors.append("JWT_SECRET_KEY too short (minimum 32 characters)")
        
        # Check if using Secret Manager in production
        if not self.settings.USE_SECRET_MANAGER:
            self.warnings.append("Not using Secret Manager - consider enabling for production")
        
        # Check security features
        if not self.settings.ENABLE_INPUT_VALIDATION:
            self.errors.append("Input validation disabled - security risk")
        if not self.settings.ENABLE_PROMPT_INJECTION_DETECTION:
            self.errors.append("Prompt injection detection disabled - security risk")
        if not self.settings.ENABLE_PII_DETECTION:
            self.errors.append("PII detection disabled - security risk")
        
        print("✅ Security settings validated")
        return True
    
    async def run_validation(self) -> Tuple[bool, List[str], List[str]]:
        """Run complete production validation"""
        print("🚀 Starting Production Validation...")
        print("=" * 50)
        
        # Run all validations
        validations = [
            self.validate_environment_variables(),
            self.validate_settings(),
            self.validate_production_config(),
            await self.validate_redis_connection(),
            await self.validate_external_services(),
            self.validate_file_permissions(),
            self.validate_security_settings()
        ]
        
        # Check if all critical validations passed
        critical_validations = validations[:4]  # First 4 are critical
        success = all(critical_validations)
        
        print("\n" + "=" * 50)
        print("📊 VALIDATION RESULTS")
        print("=" * 50)
        
        if self.errors:
            print(f"❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.warnings:
            print(f"⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        if not self.errors and not self.warnings:
            print("✅ All validations passed!")
        elif not self.errors:
            print("✅ Critical validations passed (warnings only)")
        else:
            print("❌ Critical validations failed")
        
        print(f"\nOverall Status: {'PASS' if success else 'FAIL'}")
        
        return success, self.errors, self.warnings


async def main():
    """Main validation function"""
    validator = ProductionValidator()
    success, errors, warnings = await validator.run_validation()
    
    if not success:
        print("\n❌ Production validation failed!")
        print("Please fix the errors above before starting the service.")
        sys.exit(1)
    
    if warnings:
        print("\n⚠️  Production validation passed with warnings.")
        print("Service will start but consider addressing the warnings.")
    else:
        print("\n✅ Production validation passed!")
        print("Service is ready to start.")
    
    sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
